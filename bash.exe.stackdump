Stack trace:
Frame         Function      Args
0007FFFFBB40  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFBB40, 0007FFFFAA40) msys-2.0.dll+0x2116E
0007FFFFBB40  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBB40  0002100469F2 (00021028DF99, 0007FFFFB9F8, 0007FFFFBB40, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBB40  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFBB40  00021006A525 (0007FFFFBB50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFBB50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDAA230000 ntdll.dll
7FFDA8CE0000 KERNEL32.DLL
7FFDA7CC0000 KERNELBASE.dll
7FFDA9F80000 USER32.dll
7FFDA7B60000 win32u.dll
7FFDA8460000 GDI32.dll
7FFDA80C0000 gdi32full.dll
7FFDA7C20000 msvcp_win.dll
7FFDA7A60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDA8310000 advapi32.dll
7FFDA83C0000 msvcrt.dll
7FFDA9B40000 sechost.dll
7FFDA8E80000 RPCRT4.dll
7FFDA78D0000 bcrypt.dll
7FFDA7180000 CRYPTBASE.DLL
7FFDA7B90000 bcryptPrimitives.dll
7FFDA8BF0000 IMM32.DLL
