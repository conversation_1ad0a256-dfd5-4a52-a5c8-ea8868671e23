import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { WorkersRoutingModule } from './workers-routing.module';
import { WorkersComponent } from './Component/workers.component';
import { WorkerDetailsComponent } from './Component/worker-details.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { GridModule } from '@progress/kendo-angular-grid';
import { InputsModule } from '@progress/kendo-angular-inputs';


@NgModule({
  declarations: [
    WorkersComponent,
    WorkerDetailsComponent
  ],
  imports: [
    CommonModule,
    WorkersRoutingModule,
    ButtonModule,
    InputsModule,
    DateInputsModule,
    ReactiveFormsModule,
    GridModule,
    FormsModule,
  ]
})
export class WorkersModule { }
