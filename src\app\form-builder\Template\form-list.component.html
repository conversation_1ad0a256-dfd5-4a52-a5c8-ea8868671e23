<div class="list-manager-container">

  <ng-container >
  <!-- Sidebar List Panel -->
  <div class="sidebar">
    <div class="sidebar-header">
      <h2>List Manager</h2>
       <div class="header-icon" title="Add List" (click)="showPopup = true">
        <i class="fas fa-plus"></i>
      </div>
    </div>
    <nav class="sidebar-nav">
    <ul class="nav-list">
      <li *ngFor="let list of this.lists" class="nav-item" (click)="selectList(list)">
      <a class="nav-link" [class.active]="selectedList?.id === list.id">
        <span class="nav-text">{{ list.auditHistory.listName }}</span>
        </a>
      </li>
      <li *ngIf="this.lists.length === 0" class="empty-list-message">
        No lists available. Click 'Create List' to add one.
      </li>
    </ul>
      </nav>
  </div>

  <!-- Right Panel -->
  <div class="right-panel" *ngIf="selectedList">
    <div class="panel-header">
      <h2>{{ selectedList.auditHistory.listName }}</h2>
      <div class="header-actions">
        <div class="Gropued-List" (click)="isListGroup = !isListGroup" title="Grouped List">
          <i class="fa-solid fa-toggle-on" *ngIf="isListGroup"></i><i class="fa-solid fa-toggle-off" *ngIf="!isListGroup"></i>
        </div>
        <button class="add-item-btn" (click)="addItem()" *ngIf="isListGroup">
          <i class="fas fa-plus"></i> Add
        </button>
        <button class="save-btn" (click)="saveList()">
          <i class="fas fa-save"></i> Save
        </button>
      </div>
    </div>

    <div class="item-list">
      <div *ngFor="let item of formData.list; let i = index" class="item-card">
        <div class="item-header">
          <div class="item-title">
            <input
              *ngIf="editingItem === item"
              [(ngModel)]="item.title"
              (blur)="editingItem = null"
              placeholder="Enter item name"
              (keydown.enter)="editingItem = null"
              class="editable-input"
              autofocus
            />
            <h3 *ngIf="editingItem !== item"  (click)="editItem(item)" title="Click to edit">
              {{ item.title }}
            </h3>
          </div>
          <div class="item-actions">
            <button class="action-btn add-btn" (click)="addSubItem(i)" title="Add Value">
              <i class="fas fa-plus-circle"></i>
            </button>
            <button class="action-btn delete-btn" (click)="deleteItem(i)" title="Delete Item">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>

        <!-- Item Values -->
        <div class="item-values">
          <div *ngIf="item.items.length === 0" class="empty-values-message">
            No values. Click + to add.
          </div>
          <div class="value-row" *ngFor="let sub of item.items; let j = index">
            <input
              [(ngModel)]="item.items[j].value"
              class="value-input"
              placeholder="Enter value"
            />
            <button class="remove-value-btn" (click)="removeSubItem(i, j)" title="Remove Value">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>

      <div *ngIf="formData.list.length === 0" class="empty-items-message">
        No items. Click 'Add Item' to create one.
      </div>
    </div>
  </div>

  <!-- Empty state for right panel -->
  <div class="right-panel empty-state" *ngIf="!selectedList">
    <div class="empty-message">
      <i class="fas fa-list-alt empty-icon"></i>
      <h3>No List Selected</h3>
      <p>Select a list from the sidebar or create a new one to get started.</p>
    </div>
  </div>

  <!-- Popup Modal -->
  <div *ngIf="showPopup" class="modal-overlay">
    <div class="modal">
      <div class="modal-header">
        <h3>Create New List</h3>
        <button class="close-btn" (click)="showPopup = false">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="listName">List Name</label>
          <input
            id="listName"
            [(ngModel)]="newListName"
            placeholder="Enter list name"
            class="input-field"
          />
        </div>
      </div>
      <div class="modal-footer">
        <button (click)="showPopup = false" class="cancel-btn">Cancel</button>
        <button
          (click)="addList()"
          class="create-btn"
          [disabled]="!newListName.trim()"
        >Create</button>
      </div>
    </div>
  </div>
  </ng-container>
</div>
