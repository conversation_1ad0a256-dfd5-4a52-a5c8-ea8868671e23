import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { WorkersComponent } from './Component/workers.component';
import { WorkerDetailsComponent } from './Component/worker-details.component';

const routes: Routes = [
   {
    path: '',
    redirectTo: 'List',
    pathMatch: 'full'
  },
  {
    path: 'List',
    component: WorkersComponent,
  },
  {
    path: 'Details/:id',
    component: WorkerDetailsComponent
  },
  {
    path: 'Details',
    component: WorkerDetailsComponent,
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class WorkersRoutingModule { }
