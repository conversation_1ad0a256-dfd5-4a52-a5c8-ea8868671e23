<div class="location-view-container">

<!-- Side Barr -->
  <div class="sidebar1">
    <div class="sidebar-header1">
      <div class="header-icon1">
        <i class="fas fa-map-marker-alt"></i>
      </div>
      <h3>Locations</h3>
    </div>
    <div class="sidebar-content1">
      <nav class="sidebar-nav1">
        <ul class="nav-list1">
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('Overview')"
               [ngClass]="{ 'active': selectedSection === 'Overview' }">
              <div class="nav-icon1">
                <i class="fas fa-address-card"></i>
              </div>
              <span class="nav-text1">Overview</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'Overview'"></div>
            </a>
          </li>
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('Users')"
               [ngClass]="{ 'active': selectedSection === 'Users' }">
              <div class="nav-icon1">
                <i class="fas fa-users"></i>
              </div>
              <span class="nav-text1">Users</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'Users'"></div>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

    <!-- Main Content Area -->
  <div class="main-content1">

      <ng-container *ngIf="selectedSection === 'Overview'">
      <div class="content-section1">
        <div class="section-header1">
          <div class="section-title1">
            <i class="fas fa-map-marker-alt"></i>
            <h2>Location</h2>
          </div>
          <p class="section-description1"></p>
        </div>
        <div class="content-card1">
          <div class="card-content1">
            <!-- Basic Details Section -->
            <div class="details-section">
              <h3 class="details-section-title">Basic Details</h3>

              <form [formGroup]="basicDetailsForm" (ngSubmit)="onSubmit()">
                <!-- Name Field -->
                <div class="detail-field">
                  <div class="field-label">Name</div>
                  <div class="field-value">
                    <input
                      type="text"
                      formControlName="Name"
                      placeholder="Enter name"
                      class="detail-input"
                      [class.error]="isFieldInvalid('Name')">
                    <div class="error-message" *ngIf="isFieldInvalid('Name')">
                      {{ getFieldErrorMessage('Name') }}
                    </div>
                  </div>
                </div>

                <!-- Location Code -->
                 <div class="detail-field">
                  <div class="field-label">Location Code</div>
                  <div class="field-value">
                    <input
                      type="text"
                      formControlName="LocationCode"
                      placeholder="Enter Location Code"
                      class="detail-input"
                      [class.error]="isFieldInvalid('LocationCode')">
                    <div class="error-message" *ngIf="isFieldInvalid('LocationCode')">
                      {{ getFieldErrorMessage('LocationCode') }}
                    </div>
                  </div>
                </div>

                <!-- Description Field -->
                <div class="detail-field">
                  <div class="field-label">Description</div>
                  <div class="field-value">
                    <input
                      type="text"
                      formControlName="Description"
                      placeholder="Enter description"
                      class="detail-input"
                      [class.error]="isFieldInvalid('Description')">
                    <div class="error-message" *ngIf="isFieldInvalid('Description')">
                      {{ getFieldErrorMessage('Description') }}
                    </div>
                  </div>
                </div>

                <!-- Address Field -->
                <div class="detail-field">
                  <div class="field-label">Address</div>
                  <div class="field-value">
                    <input
                      type="text"
                      formControlName="Address"
                      placeholder="Enter address"
                      class="detail-input"
                      [class.error]="isFieldInvalid('Address')">
                    <div class="error-message" *ngIf="isFieldInvalid('Address')">
                      {{ getFieldErrorMessage('Address') }}
                    </div>
                  </div>
                </div>

                <!-- Date Fields Row -->
                <div class="detail-field date-fields-row">
                  <div class="date-field-group">
                    <div class="field-label">Start Date</div>
                    <div class="field-value">
                      <input
                        type="date"
                        formControlName="StartDate"
                        placeholder="Start Date"
                        class="detail-datepicker"
                        [class.error]="isFieldInvalid('StartDate')">
                      <div class="error-message" *ngIf="isFieldInvalid('StartDate')">
                        {{ getFieldErrorMessage('StartDate') }}
                      </div>
                    </div>
                  </div>

                  <div class="date-field-group">
                    <div class="field-label">End Date</div>
                    <div class="field-value">
                      <input
                        type="date"
                        formControlName="EndDate"
                        placeholder="End Date"
                        class="detail-datepicker"
                        [class.error]="isFieldInvalid('EndDate')">
                      <div class="error-message" *ngIf="isFieldInvalid('EndDate')">
                        {{ getFieldErrorMessage('EndDate') }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons - Only show when form is dirty -->
                <div class="form-actions" *ngIf="isFormDirty">
                  <button type="submit" class="save-button">
                    Save Changes
                  </button>
                  <button type="button" class="cancel-button" (click)="onCancel()">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </ng-container>

    <ng-container *ngIf="selectedSection === 'Users' ">
      <div class="content-section1">
        <div class="content-card1">
          <div class="card-content1">

            <!-- User Management Section -->
            <div class="loc-user-management-section">

              <!-- Header with Add User Button -->
              <div class="loc-user-header">
                <div class="loc-user-title">
                  <i class="fas fa-users"></i>
                  <h3>Assigned Users</h3>
                  <span class="loc-user-count">({{ AssignUsers.length }})</span>
                </div>
                <button
                  type="button"
                  class="loc-add-user-btn"
                  (click)="openAddUserModal()"
                  [disabled]="availableUsers.length === 0">
                  <i class="fas fa-plus"></i>
                  Add User
                </button>
              </div>

              <!-- Loading State -->
              <div *ngIf="isLoadingUsers" class="loc-loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading users...</span>
              </div>

              <!-- Users List -->
              <div *ngIf="!isLoadingUsers" class="loc-users-container">

                <!-- No Users Message -->
                <div *ngIf="AssignUsers.length === 0" class="loc-no-users">
                  <i class="fas fa-user-slash"></i>
                  <h4>No Users Assigned</h4>
                  <p>This location doesn't have any users assigned yet.</p>
                  <button
                    type="button"
                    class="loc-add-first-user-btn"
                    (click)="openAddUserModal()"
                    [disabled]="availableUsers.length === 0">
                    <i class="fas fa-plus"></i>
                    Add First User
                  </button>
                </div>

                <!-- Users Grid -->
                <div *ngIf="AssignUsers.length > 0" class="loc-users-grid" [ngClass]="getGridClass()">
                  <div
                    *ngFor="let user of AssignUsers"
                    class="loc-user-card"
                    (dblclick)="navigateToUserDetails(user.id!)">

                    <!-- User Avatar -->
                    <div class="loc-user-avatar">
                      <i class="fas fa-user"></i>
                    </div>

                    <!-- User Info -->
                    <div class="loc-user-info">
                      <h4 class="loc-user-name">{{ getUserDisplayName(user) }}</h4>
                      <p class="loc-user-job">{{ getUserJobTitle(user) }}</p>
                      <p class="loc-user-email">{{ getUserEmail(user) }}</p>
                    </div>

                    <!-- Remove Button -->
                    <button
                      type="button"
                      class="loc-remove-user-btn"
                      (click)="removeUserFromLocation(user.id!)"
                      title="Remove user from location">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Add User Modal -->
            <div *ngIf="showAddUserModal" class="loc-modal-overlay" (click)="closeAddUserModal()">
              <div class="loc-modal-content" (click)="$event.stopPropagation()">
                <div class="loc-modal-header">
                  <h3>Add User to Location</h3>
                  <button type="button" class="loc-modal-close" (click)="closeAddUserModal()" title="Close modal">
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <div class="loc-modal-body">
                  <div class="loc-select-user-section">
                    <label class="loc-select-label">Select User:</label>
                    <select
                      [(ngModel)]="selectedUserToAdd"
                      class="loc-user-select"
                      title="Select user to add to location">
                      <option value="">Choose a user...</option>
                      <option
                        *ngFor="let user of availableUsers"
                        [value]="user.id">
                        {{ user.details.name }} - {{ user.details.jobtitle }}
                      </option>
                    </select>
                  </div>

                  <div *ngIf="availableUsers.length === 0" class="loc-no-available-users">
                    <i class="fas fa-info-circle"></i>
                    <p>All users are already assigned to this location.</p>
                  </div>
                </div>

                <div class="loc-modal-footer">
                  <button
                    type="button"
                    class="loc-modal-cancel-btn"
                    (click)="closeAddUserModal()">
                    Cancel
                  </button>
                  <button
                    type="button"
                    class="loc-modal-add-btn"
                    (click)="addUserToLocation()"
                    [disabled]="!selectedUserToAdd">
                    <i class="fas fa-plus"></i>
                    Add User
                  </button>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </ng-container>


  </div>
</div>
