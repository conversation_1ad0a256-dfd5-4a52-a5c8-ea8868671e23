<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Forgot Password</h2>
      <p>Enter your email address and we'll send you instructions to reset your password.</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">{{error}}</div>
    <div *ngIf="successMessage" class="alert alert-success">{{successMessage}}</div>

    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input 
          type="email" 
          formControlName="email" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" 
          placeholder="Enter your email"
        />
        <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
          <div *ngIf="f['email'].errors['required']">Email is required</div>
          <div *ngIf="f['email'].errors['email']">Please enter a valid email address</div>
        </div>
      </div>

      <div class="form-group">
        <button [disabled]="loading" class="btn btn-primary btn-block">
          <span *ngIf="loading" class="spinner-border spinner-border-sm mr-1"></span>
          Reset Password
        </button>
      </div>

      <div class="auth-links">
        <a routerLink="/auth/login">Back to Login</a>
      </div>
    </form>
  </div>
</div>
