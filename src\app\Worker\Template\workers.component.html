<div class="Workers-contanier">
  <kendo-grid
  [data]="gridData"
  [height]="200"
  filterable="menu"
  adaptiveMode="auto"
  [pageSize]="state.take"
  [skip]="state.skip"
  [sort]="state.sort"
  [height]="gridHeight"
  [pageable]="true"
  [filter]="state.filter"
  [sortable]="{allowUnsort: true,mode:'multiple'}"
  (dataStateChange)="dataStateChange($event)"
  (sortChange)="sortChange($event)"
  (dblclick)="onCellClick($event)"
  [scrollable]="'scrollable'">

   <ng-template kendoGridToolbarTemplate position="top">
     <kendo-grid-spacer></kendo-grid-spacer>
        <button kendoButton themeColor="primary" (click)="onLocation()">Add new</button>
    </ng-template>

 <kendo-grid-column field="name" title="Name" [width]="200">
  </kendo-grid-column>

  <kendo-grid-column field="jobtitle" title="Job Title" [width]="200" >
  </kendo-grid-column>

  <kendo-grid-column field="employeeNumber" title="Employee Number" [width]="150">
  </kendo-grid-column>

  </kendo-grid>
</div>
