import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GridModule } from '@progress/kendo-angular-grid';
import { LocationRoutingModule } from './location-routing.module';
import { LocationDataComponent } from './Component/location-data.component';
import { FormsModule } from '@angular/forms';
import { LocationViewComponent } from './Component/location-view.component';
import { ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';

@NgModule({
  declarations: [
    LocationDataComponent,
    LocationViewComponent
  ],
  imports: [
    ButtonModule,
     InputsModule,
    DateInputsModule,
    ReactiveFormsModule,
    LocationRoutingModule,
    GridModule,
    CommonModule,
    FormsModule,
  ]
})
export class LocationModule { }
