import { APP_INITIALIZER, CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule } from '@angular/forms';
import { AppComponent } from './app.component';
import { RouterModule } from '@angular/router';
import { HTTP_INTERCEPTORS, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClient } from '@angular/common/http';
import { HomeModule } from './home/<USER>';
import { AppRoutingModule } from './app-routing.module';
import { FormBuilderModule } from './form-builder/form-builder.module';
import { HttpIntercepter } from './SharedData/http-intercepter';
import { MsalModule, MsalRedirectComponent, MSAL_INSTANCE, MsalService, } from '@azure/msal-angular';
import { PublicClientApplication, InteractionType, IPublicClientApplication } from '@azure/msal-browser';
import { ToastrModule } from 'ngx-toastr';
import { AuthModule } from './Authentication/auth.module';
import { TokenInterceptor } from './Authentication/Services/token.interceptor';
import { CompanyIntregationComponent } from './Company/Component/company-intregation.component';
import { MsalAuthService } from './SharedData/MsalAuthService';
import { LocationDataComponent } from './Locations/Component/location-data.component';
import { LocationModule } from './Locations/location.module';
import { CompanysModule } from './Company/companys.module';
import { WorkersComponent } from './Worker/Component/workers.component';
import { WorkersModule } from './Worker/workers.module';



export function MSALInstanceFactory(msalConfigService: MsalAuthService): IPublicClientApplication {
  const instance = new PublicClientApplication(msalConfigService.getMsalConfig());
  return instance;
}
export function initializeMsal(instance: IPublicClientApplication): () => Promise<void> {
  return () => instance.initialize();
}

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    FormsModule,
    AuthModule,
    RouterModule,
    AppRoutingModule,
    HomeModule,
    LocationModule,
    CompanysModule,
    FormBuilderModule,
    WorkersModule,
    ToastrModule.forRoot({
      timeOut: 1500,
      progressBar: true,
      progressAnimation: 'increasing',
      easeTime: '150',
      easing: 'ease-in',
      positionClass: 'toast-top-right',
      tapToDismiss: true,
      preventDuplicates: true,
      resetTimeoutOnDuplicate: true,
    }),
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: HttpIntercepter, multi: true },
    MsalAuthService,
     {
      provide: APP_INITIALIZER,
      useFactory: initializeMsal,
      deps: [MSAL_INSTANCE],
      multi: true
    },
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory,
      deps: [MsalAuthService]
    },
    MsalService,
    provideHttpClient(withInterceptorsFromDi())
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA ],
  bootstrap: [AppComponent]
})
export class AppModule { }
