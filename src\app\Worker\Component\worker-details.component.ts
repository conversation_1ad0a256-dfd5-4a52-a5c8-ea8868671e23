import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { workers, workersData } from '../Models/Worker-Model';
import { Location } from '../../Locations/Models/location';
import { ActivatedRoute, Router } from '@angular/router';
import { CoreDataService } from '../../core-data.service';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-worker-details',
  templateUrl: './../Template/worker-details.component.html',
  styleUrl: './../Styles/worker-details.component.css'
})
export class WorkerDetailsComponent implements OnInit {
selectedSection: string = 'Profile';

workerForm!: FormGroup;

isFormDirty: boolean = false;

AssignLocation:Location[] = [];
allLocation: Location[] = [];
availableLocation: Location[] = [];
showLocationModal: boolean = false;
selectedLocationToAdd: string = '';
isLoadingLocation: boolean = false;

constructor(
private fb: FormBuilder,
private route: ActivatedRoute,
private router: Router,
private coreData: CoreDataService
) {}

originalFormInputValues: workers = {
 id:'',
 details:{
  name:'',
  jobtitle:'',
  email:'',
  phoneno1:'',
  phoneno2:'',
  address:'',
  city:'',
  zipCode: undefined,
  employeeNumber:'',
  hiredDate:new Date(),
  eContact1: undefined,
  eContact2:undefined,
  notes:'',
 }
};
public ids:string = '';
ngOnInit() {
   const id = this.route.snapshot.paramMap.get('id');
   if (id) {
    this.ids = id;
    this.loadLocationAndUsers(id);
   }else{
     this.initilizeForm();
   }
}

loadLocationAndUsers(UserID: string) { //this Function is For render User which is Added in Locations
  this.isLoadingLocation = true;

  forkJoin({
    USERS: this.coreData.GetUserByID(UserID),
    Locations: this.coreData.GetLocationForGrid()
  }).subscribe({
    next: ({ USERS, Locations }) => {
      this.originalFormInputValues = USERS;
      this.allLocation = Locations;

      const assignedLocationIds = USERS.auditHistory?.locationIds || [];
      this.initilizeForm();
      this.AssignLocation = Locations.filter((loc: workers) => assignedLocationIds.includes(loc.id));
      console.log(this.AssignLocation, "AssignLocation for this User");
      this.availableLocation = Locations.filter((loc: workers) => !assignedLocationIds.includes(loc.id));

      this.isLoadingLocation = false;
    },
    error: (err) => {
      console.error('Error loading User or Location :', err);
      this.isLoadingLocation = false;
    }
  });
}



initilizeForm(){
  this.workerForm = this.fb.group({
      Name: [this.originalFormInputValues.details.name, Validators.required],
      Jobtitle: [this.originalFormInputValues.details.jobtitle],
      Email: [this.originalFormInputValues.details.email, [Validators.required, Validators.email]],
      phoneno1: [this.originalFormInputValues.details.phoneno1],
      phoneno2: [this.originalFormInputValues.details.phoneno2],
      Address: [this.originalFormInputValues.details.address],
      City: [this.originalFormInputValues.details.city],
      zipCode: [this.originalFormInputValues.details.zipCode],
      EmployeeNumber: [this.originalFormInputValues.details.employeeNumber],
      HiredDate: [this.originalFormInputValues.details.hiredDate],
      EContact1: [this.originalFormInputValues.details.eContact1],
      EContact2: [this.originalFormInputValues.details.eContact2],
      Notes: [this.originalFormInputValues.details.notes],
    });

     this.workerForm.valueChanges.subscribe(() => {
    this.checkIfFormIsDirty();
  });
}
checkIfFormIsDirty(): void {
  const currentValues = this.workerForm.value;
  this.isFormDirty = JSON.stringify(currentValues) !== JSON.stringify(this.originalFormInputValues);
}

// Helper method to check if a field has errors and is touched
isFieldInvalid(fieldName: string): boolean {
  const field = this.workerForm.get(fieldName);
  return !!(field && field.invalid && (field.dirty || field.touched));
}

// Helper method to get error message for a field
getFieldErrorMessage(fieldName: string): string {
  const field = this.workerForm.get(fieldName);
  if (field && field.errors && (field.dirty || field.touched)) {
    if (field.errors['required']) {
      return `${this.getFieldDisplayName(fieldName)} is required`;
    }
    if (field.errors['minlength']) {
      const requiredLength = field.errors['minlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters`;
    }
    if (field.errors['maxlength']) {
      const maxLength = field.errors['maxlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} cannot exceed ${maxLength} characters`;
    }
    if (field.errors['email']) {
      return `Please enter a valid email address`;
    }
  }
  return '';
}

// Helper method to get display name for field
private getFieldDisplayName(fieldName: string): string {
  const displayNames: { [key: string]: string } = {
    'Name': 'Name',
    'Jobtitle': 'Job Title',
    'Email': 'Email',
    'phoneno1': 'Phone Number',
    'phoneno2': 'Phone Number',
    'Address': 'Address',
    'City': 'City',
    'zipCode': 'Zip Code',
    'EmployeeNumber':'Employee Number',
    'HiredDate':'Hired Date',
    'EContact1':'Emergency Contact Number',
    'EContact2': 'Emergency Contact Number',
    'Notes':'Notes',
  };
  return displayNames[fieldName] || fieldName;
}

onSubmit() {
  // Mark all fields as touched to show validation errors
  this.workerForm.markAllAsTouched();

  if (this.workerForm.valid) {
      const UserData = this.workerForm.value;

    const UserDataforsave = {
      id: this.ids,
      details: UserData,
      auditHistory: {
        createdBy: "Aman",         // You can dynamically set this from login data
        updatedBy: "Aman",
        status: "Approved",
        createdDate: new Date(),
        updatedDate: new Date()
      }
    };

    this.coreData.SaveUser(UserDataforsave).subscribe({
      next:(response)=>{
         this.originalFormInputValues = { ...UserData  };
          this.isFormDirty = false;
      }
    })
    } else {
      console.log('Form is invalid - please check the errors above');
    }
}

 onCancel() {
    // Reset form to original values
    this.workerForm.reset({
      Name: this.originalFormInputValues.details.name,
      Jobtitle:this.originalFormInputValues.details.jobtitle,
      Email: this.originalFormInputValues.details.email,
      Phoneno1:this.originalFormInputValues.details.phoneno1,
      Phoneno2: this.originalFormInputValues.details.phoneno2,
      Address: this.originalFormInputValues.details.address,
      City:this.originalFormInputValues.details.city,
      ZipCode:this.originalFormInputValues.details.zipCode,
      EmployeeNumber:this.originalFormInputValues.details.employeeNumber,
      HiredDate:this.originalFormInputValues.details.hiredDate,
      EContact1:this.originalFormInputValues.details.eContact1,
      EContact2:this.originalFormInputValues.details.eContact2,
      Notes:this.originalFormInputValues.details.notes
    });
    this.isFormDirty = false;
  }

 selectSection(text:string){
  this.selectedSection = text;
}

openAddLocationModal(): void {
  this.showLocationModal = true;
  this.selectedLocationToAdd = '';
}

getGridClass(): string {
  const cardCount = this.AssignLocation.length;
  if (cardCount <= 2) {
    return 'user-locations-grid-flex';
  } else {
    return 'user-locations-grid-grid';
  }
}

navigateToLocationDetails(LocationId: string){
  this.router.navigate(['Locations/View', LocationId]);
}

getLocationDisplayName(Location:Location){
  return Location.details.name || null;
}

closeAddLocationModal(): void {
  this.showLocationModal = false;
  this.selectedLocationToAdd = '';
}


addLocationToUser():void{
    if (this.selectedLocationToAdd && this.ids) {
    this.coreData.assignLocationtoUser(this.ids, this.selectedLocationToAdd).subscribe({
      next: (response) => {
        console.log('Location assigned successfully:', response);

        this.loadLocationAndUsers(this.ids); // Refresh the user lists
        // You can add a success message here
        this.closeAddLocationModal();
      },
      error: (error) => {
        console.error('Error assigning user:', error);
        // You can add an error message here
      }
    });
  }
}

removeLocationFromUser(LocationID: string){
    this.coreData.removeLocationtoUser(this.ids, LocationID).subscribe({
      next: (response) => {
        console.log('Location removed successfully:', response);
        this.loadLocationAndUsers(this.ids); // Refresh the user lists
        // You can add a success message here
      },
      error: (error) => {
        console.error('Error removing user:', error);
        // You can add an error message here
      }
    });
  }
}

