import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Location } from '../Models/location';
import { ActivatedRoute, Router } from '@angular/router';
import { CoreDataService } from '../../core-data.service';
import { workers } from '../../Worker/Models/Worker-Model';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-location-view',
  templateUrl: './../Template/location-view.component.html',
  styleUrl: './../Styles/location-view.component.css'
})
export class LocationViewComponent implements OnInit {
constructor(private fb: FormBuilder,
  private route: ActivatedRoute,
  private coreData: CoreDataService,
  private router: Router
  ){}

basicDetailsForm!: FormGroup;

selectedSection: string = 'Overview';

isFormDirty: boolean = false;

AssignUsers:workers[] = [];
allUsers: workers[] = [];
availableUsers: workers[] = [];
showAddUserModal: boolean = false;
selectedUserToAdd: string = '';
isLoadingUsers: boolean = false;

// Form values for HTML inputs (dates as strings)
originalFormInputValues: Location = {
  id:'',
  details:{
    name: '',
    locationCode: '',
    description: '',
    address: '',
    startDate: new Date(''),
    endDate: new Date(''),
  },
};
public ids:string = '';
ngOnInit(): void {

  const id = this.route.snapshot.paramMap.get('id');
   if (id) {
    this.ids = id;
    this.loadLocationAndUsers(id);
   }else{
     this.initializeForm();
   }

}



loadLocationAndUsers(locationId: string) { //this Function is For render User which is Added in Locations
  this.isLoadingUsers = true;

  forkJoin({
    location: this.coreData.getLocationByID(locationId),
    users: this.coreData.GetUsers()
  }).subscribe({
    next: ({ location, users }) => {
      this.originalFormInputValues = location;
      this.allUsers = users;

      const assignedUserIds = location.auditHistoryLoc?.userIds || [];
      this.initializeForm();
      this.AssignUsers = users.filter((user: workers) => assignedUserIds.includes(user.id));
      this.availableUsers = users.filter((user: workers) => !assignedUserIds.includes(user.id));

      this.isLoadingUsers = false;
    },
    error: (err) => {
      console.error('Error loading location or users:', err);
      this.isLoadingUsers = false;
    }
  });
}


initializeForm(): void {
  this.basicDetailsForm = this.fb.group({
    Name: [this.originalFormInputValues.details.name, [Validators.required, Validators.minLength(2)]],
    LocationCode:[this.originalFormInputValues.details.locationCode,[Validators.required]],
    Description: [this.originalFormInputValues.details.description, [Validators.maxLength(200)]],
    Address: [this.originalFormInputValues.details.address, [Validators.required, Validators.minLength(5)]],
    StartDate: [this.originalFormInputValues.details.startDate],
    EndDate: [this.originalFormInputValues.details.endDate]
  });

  // Subscribe to form value changes to track if form is dirty
  this.basicDetailsForm.valueChanges.subscribe(() => {
    this.checkIfFormIsDirty();
  });
}

checkIfFormIsDirty(): void {
  const currentValues = this.basicDetailsForm.value;
  this.isFormDirty = JSON.stringify(currentValues) !== JSON.stringify(this.originalFormInputValues);
}

// Helper method to check if a field has errors and is touched
isFieldInvalid(fieldName: string): boolean {
  const field = this.basicDetailsForm.get(fieldName);
  return !!(field && field.invalid && (field.dirty || field.touched));
}

// Helper method to get error message for a field
getFieldErrorMessage(fieldName: string): string {
  const field = this.basicDetailsForm.get(fieldName);
  if (field && field.errors && (field.dirty || field.touched)) {
    if (field.errors['required']) {
      return `${this.getFieldDisplayName(fieldName)} is required`;
    }
    if (field.errors['minlength']) {
      const requiredLength = field.errors['minlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters`;
    }
    if (field.errors['maxlength']) {
      const maxLength = field.errors['maxlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} cannot exceed ${maxLength} characters`;
    }
  }
  return '';
}

// Helper method to get display name for field
private getFieldDisplayName(fieldName: string): string {
  const displayNames: { [key: string]: string } = {
    'Name': 'Name',
    'LocationCode': 'Location Code',
    'Description': 'Description',
    'Address': 'Address',
    'StartDate': 'Start Date',
    'EndDate': 'End Date'
  };
  return displayNames[fieldName] || fieldName;
}

  onSubmit() {
    // Mark all fields as touched to show validation errors
    this.basicDetailsForm.markAllAsTouched();

    if (this.basicDetailsForm.valid) {
      const formValues = this.basicDetailsForm.value;

      const LocData = {
      id:this.ids, //if id get from Location ID;
      details: formValues,
      auditHistoryLoc: {
        createdBy: "Aman",         // You can dynamically set this from login data
        updatedBy: "Aman",
        status: "Approved",
        userID: "",
        createdDate: new Date(),
        updatedDate: new Date()
      }
    };
    console.log(LocData , "SaavingData from fronted");
      // Add your save logic here - use locationDetails for backend API
      this.coreData.SaveLocation(LocData).subscribe({
        next:(response)=>{
          // After successful save, update original values and reset dirty state
          this.originalFormInputValues = { ...formValues  };
          this.isFormDirty = false;
        }
      });
    } else {
      console.log('Form is invalid - please check the errors above');
    }
  }

onCancel() {
  // Only reset the 'details' part of originalFormInputValues
  this.basicDetailsForm.reset({
    Name: this.originalFormInputValues.details.name,
    LocationCode: this.originalFormInputValues.details.locationCode,
    Description: this.originalFormInputValues.details.description,
    Address: this.originalFormInputValues.details.address,
    StartDate: this.originalFormInputValues.details.startDate,
    EndDate: this.originalFormInputValues.details.endDate
  });
  this.isFormDirty = false;
}


selectSection(section: string) {
  this.selectedSection = section;
}

// User Management Methods
openAddUserModal(): void {
  this.showAddUserModal = true;
  this.selectedUserToAdd = '';
}

closeAddUserModal(): void {
  this.showAddUserModal = false;
  this.selectedUserToAdd = '';
}

addUserToLocation(): void {
  if (this.selectedUserToAdd && this.ids) {
    this.coreData.assignUsertoLocation(this.ids, this.selectedUserToAdd).subscribe({
      next: (response) => {
        console.log('User assigned successfully:', response);

        this.loadLocationAndUsers(this.ids); // Refresh the user lists
        // You can add a success message here
        this.closeAddUserModal();
      },
      error: (error) => {
        console.error('Error assigning user:', error);
        // You can add an error message here
      }
    });
  }
}

removeUserFromLocation(userId: string): void {
  if (confirm('Are you sure you want to remove this user from the location?')) {
    this.coreData.removeUsertoLocation(this.ids, userId).subscribe({
      next: (response) => {
        console.log('User removed successfully:', response);
        this.loadLocationAndUsers(this.ids); // Refresh the user lists
        // You can add a success message here
      },
      error: (error) => {
        console.error('Error removing user:', error);
        // You can add an error message here
      }
    });
  }
}

navigateToUserDetails(userId: string): void {
  this.router.navigate(['Users/Details', userId]);
}

getUserDisplayName(user: workers): string {
  return user.details?.name || 'Unknown User';
}

getUserJobTitle(user: workers): string {
  return user.details?.jobtitle || 'No Job Title';
}

getUserEmail(user: workers): string {
  return user.details?.email || 'No Email';
}

getGridClass(): string {
  const cardCount = this.AssignUsers.length;
  if (cardCount <= 2) {
    return 'loc-users-grid-flex';
  } else {
    return 'loc-users-grid-grid';
  }
}

}
