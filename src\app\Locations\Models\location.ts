export interface Location{
  id?: string;
  details: locationData;
  auditHistoryLoc?: {
      userIds?: string[];
      createdBy?: string;
      updatedBy?: string;
      createdDate?: Date;
      updatedDate?: Date;
      status?: string;
      userID?: string;
    };
}
export interface locationData{
  name: string;
  locationCode: string;
  description: string;
  address: string;
  startDate: Date;
  endDate: Date;
}
