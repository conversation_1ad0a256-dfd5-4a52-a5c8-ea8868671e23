import { Component } from '@angular/core';
import { CoreChartOptions } from 'chart.js';
import { CoreDataService } from '../../core-data.service';
import { ShareService } from '../../SharedData/share-services.service';

@Component({
  selector: 'app-form-config',
  templateUrl: './../Template/form-config.component.html',
  styleUrl: './../Styles/form-config.component.css'
})
export class FormConfigComponent {

constructor(private coreDataService:CoreDataService, private shareService:ShareService){}

ngOnInit(): void {
}

}
