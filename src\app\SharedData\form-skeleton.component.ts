import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-form-skeleton',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="skeleton-form-container">
      <!-- Form Header Skeleton -->
      <div class="skeleton-form-section">
        <div class="skeleton-section-title"></div>
        <div class="skeleton-metadata">
          <div class="skeleton-metadata-row">
            <div class="skeleton-metadata-label"></div>
            <div class="skeleton-metadata-value"></div>
          </div>
          <div class="skeleton-metadata-row">
            <div class="skeleton-metadata-label"></div>
            <div class="skeleton-metadata-value"></div>
          </div>
          <div class="skeleton-metadata-row">
            <div class="skeleton-metadata-label"></div>
            <div class="skeleton-metadata-value"></div>
          </div>
        </div>
      </div>

      <!-- Form Sections Skeleton -->
      <div class="skeleton-form-section" *ngFor="let section of generateSections()">
        <div class="skeleton-section-title"></div>
        <div class="skeleton-form-fields">
          <div class="skeleton-form-group" *ngFor="let field of generateFields()">
            <div class="skeleton-label"></div>
            <div class="skeleton-input"></div>
            <div class="skeleton-action-icons">
              <div class="skeleton-action-icon"></div>
              <div class="skeleton-action-icon"></div>
              <div class="skeleton-action-icon"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions Skeleton -->
      <div class="skeleton-form-actions">
        <div class="skeleton-button"></div>
      </div>
    </div>
  `,
  styles: [`
    .skeleton-form-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 600px;
      margin: 0px 0vw;
      padding: 0px;
      box-sizing: border-box;
      gap: 4px;
    }

    .skeleton-form-section {
      border: 1px solid #ccc;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 20px;
      background-color: white;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      width: 60vw;
    }

    .skeleton-section-title {
      height: 24px;
      width: 70%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    }

    .skeleton-metadata {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .skeleton-metadata-row {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .skeleton-metadata-label {
      height: 16px;
      width: 80px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
    }

    .skeleton-metadata-value {
      height: 16px;
      width: 150px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
    }

    .skeleton-form-fields {
      padding: 8px 0;
    }

    .skeleton-form-group {
      margin-bottom: 15px;
      position: relative;
      padding: 8px;
      border-radius: 4px;
      background-color: rgba(33, 150, 243, 0.01);
    }

    .skeleton-label {
      height: 16px;
      width: 40%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 8px;
    }

    .skeleton-input {
      height: 7vh;
      min-height: 36px;
      width: 100%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      border-bottom: 3px solid #cccc;
    }

    .skeleton-action-icons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 8px;
    }

    .skeleton-action-icon {
      height: 16px;
      width: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 50%;
    }

    .skeleton-form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }

    .skeleton-button {
      height: 36px;
      width: 120px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
    }

    @keyframes shimmer {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }

    /* Mobile responsive styles */
    @media (max-width: 767px) {
      .skeleton-form-container {
        padding: 15px 10px;
        max-width: 100%;
        margin: 0;
        flex-direction: column;
      }

      .skeleton-form-section {
        padding: 12px 10px;
        border-radius: 8px;
        margin-bottom: 15px;
        width: 100%;
      }

      .skeleton-section-title {
        height: 20px;
        margin-bottom: 12px;
      }

      .skeleton-metadata-label {
        width: 60px;
        height: 14px;
      }

      .skeleton-metadata-value {
        width: 120px;
        height: 14px;
      }

      .skeleton-form-group {
        margin-bottom: 12px;
        padding: 6px;
      }

      .skeleton-label {
        height: 14px;
        margin-bottom: 6px;
        width: 50%;
      }

      .skeleton-input {
        height: 36px;
      }

      .skeleton-action-icons {
        margin-top: 6px;
      }

      .skeleton-action-icon {
        height: 14px;
        width: 14px;
      }

      .skeleton-button {
        width: 100%;
      }
    }
  `]
})
export class FormSkeletonComponent {
  @Input() sections: number = 3;
  @Input() fieldsPerSection: number = 4;

  generateSections(): number[] {
    return Array(this.sections).fill(0);
  }

  generateFields(): number[] {
    return Array(this.fieldsPerSection).fill(0);
  }
}
