<div class="Workers-Data-View">

  <!-- SideBar -->
    <div class="sidebar1">
    <div class="sidebar-header1">
      <div class="header-icon1">
        <i class="fas fa-user"></i>
      </div>
      <h3>Users</h3>
    </div>
    <div class="sidebar-content1">
      <nav class="sidebar-nav1">
        <ul class="nav-list1">
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('Profile')"
               [ngClass]="{ 'active': selectedSection === 'Profile' }">
              <div class="nav-icon1">
                <i class="fas fa-address-card"></i>
              </div>
              <span class="nav-text1">Profile</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'Profile'"></div>
            </a>
          </li>
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('Current Locations')"
               [ngClass]="{ 'active': selectedSection === 'Current Locations' }">
              <div class="nav-icon1">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <span class="nav-text1">Current Locations</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'Current Locations'"></div>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

    <div class="main-content1">
        <ng-container *ngIf="selectedSection === 'Profile'">
          <div class="content-section1">
        <!-- <div class="section-header1">
          <div class="section-title1">
            <i class="fas fa-map-marker-alt"></i>
            <h2>Location</h2>
          </div>
          <p class="section-description1"></p>
        </div> -->
        <div class="content-card1">
          <div class="card-content1">
            <form [formGroup]="workerForm" (ngSubmit)="onSubmit()">

              <!-- Basic Info Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-user section-icon"></i>
                  <h3 class="section-title">Basic Info</h3>
                </div>

                <div class="form-grid">
                  <!-- Full Name -->
                  <div class="form-field">
                    <label class="field-label">Full Name</label>
                    <input
                      type="text"
                      formControlName="Name"
                      placeholder="Enter Your Name"
                      class="field-input"
                      [class.error]="isFieldInvalid('Name')">
                    <div class="error-message" *ngIf="isFieldInvalid('Name')">
                      {{ getFieldErrorMessage('Name') }}
                    </div>
                  </div>

                  <!-- Last Name (placeholder for future use) -->
                  <!-- <div class="form-field">
                    <label class="field-label">Last Name</label>
                    <input
                      type="text"
                      placeholder="Askew"
                      class="field-input">
                  </div> -->

                  <!-- Job Title -->
                  <div class="form-field full-width">
                    <label class="field-label">Job Title</label>
                    <input
                      type="text"
                      formControlName="Jobtitle"
                      placeholder="Job Title"
                      class="field-input"
                      [class.error]="isFieldInvalid('Jobtitle')">
                    <div class="error-message" *ngIf="isFieldInvalid('Jobtitle')">
                      {{ getFieldErrorMessage('Jobtitle') }}
                    </div>
                  </div>

                  <!-- Photo Upload Placeholder -->
                  <div class="photo-upload">
                    <!-- <div class="photo-placeholder">
                      <i class="fas fa-camera"></i>
                      <span>Add Photo</span>
                    </div> -->
                  </div>
                </div>
              </div>

              <!-- Contact Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-envelope section-icon"></i>
                  <h3 class="section-title">Contact</h3>
                </div>

                <div class="form-grid">
                  <!-- Email -->
                  <div class="form-field full-width">
                    <label class="field-label">Email</label>
                    <input
                      type="email"
                      formControlName="Email"
                      placeholder="<EMAIL>"
                      class="field-input"
                      [class.error]="isFieldInvalid('Email')">
                    <div class="error-message" *ngIf="isFieldInvalid('Email')">
                      {{ getFieldErrorMessage('Email') }}
                    </div>
                  </div>

                  <!-- Phone Number Mobile -->
                  <div class="form-field">
                    <label class="field-label">Phone Number [Mobile]</label>
                    <input
                      type="text"
                      formControlName="phoneno1"
                      placeholder="Mobile Phone"
                      class="field-input"
                      [class.error]="isFieldInvalid('phoneno1')">
                    <div class="error-message" *ngIf="isFieldInvalid('phoneno1')">
                      {{ getFieldErrorMessage('phoneno1') }}
                    </div>
                  </div>

                  <!-- Phone Number Other -->
                  <div class="form-field">
                    <label class="field-label">Phone Number [Other]</label>
                    <input
                      type="text"
                      formControlName="phoneno2"
                      placeholder="Other Phone"
                      class="field-input"
                      [class.error]="isFieldInvalid('phoneno2')">
                    <div class="error-message" *ngIf="isFieldInvalid('phoneno2')">
                      {{ getFieldErrorMessage('phoneno2') }}
                    </div>
                  </div>

                  <!-- Street Address -->
                  <div class="form-field full-width">
                    <label class="field-label">Street Address</label>
                    <input
                      type="text"
                      formControlName="Address"
                      placeholder="Street Address"
                      class="field-input"
                      [class.error]="isFieldInvalid('Address')">
                    <div class="error-message" *ngIf="isFieldInvalid('Address')">
                      {{ getFieldErrorMessage('Address') }}
                    </div>
                  </div>

                  <!-- City -->
                  <div class="form-field">
                    <label class="field-label">City</label>
                    <input
                      type="text"
                      formControlName="City"
                      placeholder="City"
                      class="field-input"
                      [class.error]="isFieldInvalid('City')">
                    <div class="error-message" *ngIf="isFieldInvalid('City')">
                      {{ getFieldErrorMessage('City') }}
                    </div>
                  </div>

                  <!-- Postal/Zip Code -->
                  <div class="form-field">
                    <label class="field-label">Postal/Zip Code</label>
                    <input
                      type="number"
                      formControlName="zipCode"
                      placeholder="Zip Code"
                      class="field-input"
                      [class.error]="isFieldInvalid('zipCode')">
                    <div class="error-message" *ngIf="isFieldInvalid('zipCode')">
                      {{ getFieldErrorMessage('zipCode') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Employment Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-briefcase section-icon"></i>
                  <h3 class="section-title">Employment</h3>
                </div>

                <div class="form-grid">
                  <!-- Employee Number -->
                  <div class="form-field">
                    <label class="field-label">Employee Number</label>
                    <input
                      type="text"
                      formControlName="EmployeeNumber"
                      placeholder="Employee Number"
                      class="field-input"
                      [class.error]="isFieldInvalid('EmployeeNumber')">
                    <div class="error-message" *ngIf="isFieldInvalid('EmployeeNumber')">
                      {{ getFieldErrorMessage('EmployeeNumber') }}
                    </div>
                  </div>

                  <!-- Date Hired -->
                  <div class="form-field">
                    <label class="field-label">Date Hired</label>
                    <input
                      type="date"
                      formControlName="HiredDate"
                      class="field-input"
                      [class.error]="isFieldInvalid('HiredDate')">
                    <div class="error-message" *ngIf="isFieldInvalid('HiredDate')">
                      {{ getFieldErrorMessage('HiredDate') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Emergency Contact Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-phone section-icon"></i>
                  <h3 class="section-title">Emergency Contact</h3>
                </div>

                <div class="form-grid">
                  <!-- Emergency Contact 1 -->
                  <div class="form-field">
                    <label class="field-label">Contact 1</label>
                    <input
                      type="text"
                      formControlName="EContact1"
                      placeholder="Emergency Contact 1"
                      class="field-input"
                      [class.error]="isFieldInvalid('EContact1')">
                    <div class="error-message" *ngIf="isFieldInvalid('EContact1')">
                      {{ getFieldErrorMessage('EContact1') }}
                    </div>
                  </div>

                  <!-- Emergency Contact 2 -->
                  <div class="form-field">
                    <label class="field-label">Contact 2</label>
                    <input
                      type="text"
                      formControlName="EContact2"
                      placeholder="Emergency Contact 2"
                      class="field-input"
                      [class.error]="isFieldInvalid('EContact2')">
                    <div class="error-message" *ngIf="isFieldInvalid('EContact2')">
                      {{ getFieldErrorMessage('EContact2') }}
                    </div>
                  </div>

                  <!-- Notes -->
                  <div class="form-field full-width">
                    <label class="field-label">Notes</label>
                    <textarea
                      formControlName="Notes"
                      placeholder="Additional notes..."
                      class="field-textarea"
                      [class.error]="isFieldInvalid('Notes')">
                    </textarea>
                    <div class="error-message" *ngIf="isFieldInvalid('Notes')">
                      {{ getFieldErrorMessage('Notes') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="form-actions" *ngIf="isFormDirty">
                <button type="submit" class="save-button">Save Changes</button>
                <button type="button" class="cancel-button" (click)="onCancel()">Cancel</button>
              </div>
            </form>
          </div>
        </div>
        </div>
        </ng-container>

        <ng-container *ngIf="selectedSection === 'Current Locations'">
          <div class="content-section1">
            <div class="content-card1">
              <div class="card-content1">

            <!-- Location Management Section -->
            <div class="user-location-management-section">

              <!-- Header with Add Location Button -->
              <div class="user-location-header">
                <div class="user-location-title">
                  <i class="fas fa-map-marker-alt"></i>
                  <h3>Assigned Locations</h3>
                  <span class="user-location-count">({{ AssignLocation.length }})</span>
                </div>
                <button
                  type="button"
                  class="user-add-location-btn"
                  (click)="openAddLocationModal()"
                  [disabled]="availableLocation.length === 0">
                  <i class="fas fa-plus"></i>
                  Add Location
                </button>
              </div>

              <!-- Loading State -->
              <div *ngIf="isLoadingLocation" class="user-loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading locations...</span>
              </div>

              <!-- Locations List -->
              <div *ngIf="!isLoadingLocation" class="user-locations-container">

                <!-- No Locations Message -->
                <div *ngIf="AssignLocation.length === 0" class="user-no-locations">
                  <i class="fas fa-map-marker-slash"></i>
                  <h4>No Locations Assigned</h4>
                  <p>This user doesn't have any locations assigned yet.</p>
                  <button
                    type="button"
                    class="user-add-first-location-btn"
                    (click)="openAddLocationModal()"
                    [disabled]="availableLocation.length === 0">
                    <i class="fas fa-plus"></i>
                    Add First Location
                  </button>
                </div>

                <!-- Locations Grid -->
                <div *ngIf="AssignLocation.length > 0" class="user-locations-grid" [ngClass]="getGridClass()">
                  <div
                    *ngFor="let loc of AssignLocation"
                    class="user-location-card"
                    (dblclick)="navigateToLocationDetails(loc.id!)">

                    <!-- Location Avatar -->
                    <div class="user-location-avatar">
                      <i class="fas fa-map-marker-alt"></i>
                    </div>

                    <!-- Location Info -->
                    <div class="user-location-info">
                      <h4 class="user-location-name">{{ getLocationDisplayName(loc) }}</h4>
                    </div>

                    <!-- Remove Button -->
                    <button
                      type="button"
                      class="user-remove-location-btn"
                      (click)="removeLocationFromUser(loc.id!)"
                      title="Remove location from user">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Add Location Modal -->
            <div *ngIf="showLocationModal" class="user-modal-overlay" (click)="closeAddLocationModal()">
              <div class="user-modal-content" (click)="$event.stopPropagation()">
                <div class="user-modal-header">
                  <h3>Add Location to User</h3>
                  <button type="button" class="user-modal-close" (click)="closeAddLocationModal()" title="Close modal">
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <div class="user-modal-body">
                  <div class="user-select-location-section">
                    <label class="user-select-label">Select Location:</label>
                    <select
                      [(ngModel)]="selectedLocationToAdd"
                      class="user-location-select"
                      title="Select location to add to user">
                      <option value="">Choose a Location...</option>
                      <option
                        *ngFor="let loc of availableLocation"
                        [value]="loc.id">
                        {{ loc.details.name }}
                      </option>
                    </select>
                  </div>

                  <div *ngIf="availableLocation.length === 0" class="user-no-available-locations">
                    <i class="fas fa-info-circle"></i>
                    <p>All locations are already assigned to this user.</p>
                  </div>
                </div>

                <div class="user-modal-footer">
                  <button
                    type="button"
                    class="user-modal-cancel-btn"
                    (click)="closeAddLocationModal()">
                    Cancel
                  </button>
                  <button
                    type="button"
                    class="user-modal-add-btn"
                    (click)="addLocationToUser()"
                    [disabled]="!selectedLocationToAdd">
                    <i class="fas fa-plus"></i>
                    Add Location
                  </button>
                </div>
              </div>
            </div>

              </div>
            </div>
          </div>
        </ng-container>
    </div>
</div>
