/* Form Builder Container */
.form-builder-container {
  height: calc(100vh - 60px);
  /* height: auto; */
  overflow: hidden;
  display: flex;
  flex-direction: row;
  background-color: white;
}

/* Selected Element Styling */
.form-element-row.selected-element {
  background-color: #e3f2fd !important;
  border: 2px solid #2196F3 !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important;
}

.form-element-row {
  cursor: pointer;
  transition: all 0.2s ease;
}


/* Initial State - Only New Form Button */
.initial-state {
  /* display: flex;
  justify-content: flex-start; */
  /* align-items: flex-start; */
  height: 100%;
  background-color: #f9f9f9;
  padding: 20px;
}

/* New Form Button */
.new-form-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  background-color: white;
  color: #4285F4;
  border: 1px solid #dadce0;
  border-radius: 24px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.new-form-btn:hover {
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.new-form-btn:active {
  background-color: #f1f3f4;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.new-form-btn i {
  margin-right: 8px;
  font-size: 16px;
  color: #4285F4;
}

@media (max-width: 768px) {
  .form-builder-container {
    height: auto;
    min-height: calc(100vh - 60px);
  }

  .new-form-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}

/* Box Content Container */
.box-content {
  display: flex;
  gap: 20px;
  width: 100%;
  height: 100%;
}

/* Main Form Building Area */
.form-building-area {
  flex: 1;
  max-width: calc(100% - 340px); /* Account for validation box width */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  padding: 20px;
  margin-left: 2vw;
}
/* Form Header */
.form-header {
  margin-bottom: 15px;
  padding-bottom: 5px;
}

.form-header h2 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.form-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.required-fields-note {
  color: #666;
  font-size: 12px;
  font-style: italic;
}

.metadata-row {
  display: flex;
  align-items: center;
}

.metadata-label {
  font-weight: bold;
  margin-right: 5px;
  color: #666;
}

.metadata-value {
  color: #333;
}

/* Form Sections */
.form-section {
  margin-bottom: 15px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #444;
  color: white;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title input {
  background: transparent;
  border: none;
  color: white;
  font-size: 14px;
  font-weight: 500;
  padding: 3px 5px;
  width: 150px;
}

.section-title input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.1);
}

.drag-handle {
  /* cursor: move; */
  color: #aaa;
  font-size: 14px;
}

.required-indicator {
  color: #ff4444;
  font-weight: bold;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-toggles {
  display: flex;
  gap: 15px;
}

.toggle-option {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.toggle-option input[type="checkbox"] {
  margin: 0;
}

.btn-icon {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 3px;
}

.btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.delete-section {
  color: #ff6b6b;
}

.collapse-btn {
  color: #e0e0e0;
}

.collapse-btn:hover {
  color: white;
}

.section-content {
  padding: 10px;
  background-color: #f9f9f9;
}

.empty-section-message {
  padding: 15px;
  text-align: center;
  color: #999;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 13px;
}

/* Form Elements List */
.form-elements-list {
  margin-bottom: 15px;
}

.form-element-row {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.form-element-row:hover {
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
  .form-element-row {
    flex-wrap: wrap;
    padding: 10px;
  }

  .element-fields {
    flex: 100%;
    order: 3;
    margin-top: 10px;
    margin-right: 0;
  }

  .element-show-label {
    order: 4;
    margin-top: 10px;
    margin-left: 34px; /* Align with the content after icons */
  }

  .element-actions {
    order: 2;
  }
}

/* Required Star Icon */
.element-required {
  margin-right: 10px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.element-required i {
  font-size: 18px;
  transition: all 0.2s ease;
}

.element-required i.required {
  color: #dc3545; /* Red color for required */
  text-shadow: 0 0 1px rgba(220, 53, 69, 0.3);
}

.element-required i.not-required {
  color: #e0e0e0; /* Light gray for not required */
}

.element-required:hover i.required {
  transform: scale(1.1);
}

.element-required:hover i.not-required {
  color: #ccc;
}

/* Element Type Icon */
.element-type-icon {
  margin-right: 15px;
  width: 24px;
  text-align: center;
}

.element-type-icon i {
  font-size: 18px;
}

.element-type-icon i.fa-font {
  color: #4CAF50;
}

.element-type-icon i.fa-envelope {
  color: #007bff;
}

.element-type-icon i.fa-hashtag {
  color: #6610f2;
}

.element-type-icon i.fa-link {
  color: #2196F3;
}

.element-type-icon i.fa-check-square {
  color: #FF9800;
}

/* Element Fields Container */
.element-fields {
  display: flex;
  flex: 4;
  gap: 15px;
  margin-right: 15px;
}

/* Element Link Text */
.element-link-text {
  flex: 1;
}

.element-link-text input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #2196F3;
  font-weight: 500;
}

/* Element Dropdown Info */
.element-dropdown-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.dropdown-source {
  padding: 8px;
  background-color: #fff3e0;
  border: 1px solid #ffe0b2;
  border-radius: 4px;
  color: #FF9800;
  font-size: 13px;
  font-weight: 500;
}

@media (max-width: 992px) {
  .element-fields {
    flex-direction: column;
    gap: 8px;
  }
}

/* Element Label */
.element-label {
  flex: 1;
}

.element-label input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-weight: bold;
}

/* Element Placeholder */
.element-placeholder {
  flex: 1;
}

.element-placeholder input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Show Label Checkbox */
.element-show-label {
  margin-right: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 5px;
}

.checkbox-wrapper input {
  margin: 0;
}

.checkbox-wrapper label {
  margin: 0;
  font-size: 14px;
  color: #666;
}

@media (max-width: 768px) {
  .element-show-label {
    margin-right: 10px;
  }

  .checkbox-wrapper label {
    font-size: 12px;
  }
}

/* Element Actions */
.element-actions {
  margin-left: auto;
  margin-right: 2px;
}
.options-wrapper {
  position: relative;
  display: inline-block;
}

.options{
  margin-left: 5px;
  color: #6e6e6e;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.options:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* Enhanced Options Menu - Override old styles */
.options-wrapper .options-menu {
  display: none;
  opacity: 0;
  transform: translateY(-10px);
  position: absolute;
  top: -35px;
  right: -25px;
  z-index: 1000;
  transition: all 0.3s ease;

  /* New enhanced styles */
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 4px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-radius: 12px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.options-wrapper:hover .options-menu {
  display: flex !important;
  opacity: 1;
  transform: translateY(0);
}

.options-wrapper .options-menu:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  border-color: #cbd5e1 !important;
}

/* Enhanced Menu Items - Override old styles */
.options-wrapper .menu-item {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  background: white !important;
  border: 1px solid transparent !important;
  padding: 0 !important;
  text-align: center !important;
}

.options-wrapper .menu-item:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2) !important;
}

.options-wrapper .menu-item:hover i {
  color: white !important;
  transform: scale(1.1) !important;
}

.options-wrapper .menu-item:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2) !important;
}

.options-wrapper .menu-item i {
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

/* Enhanced Required/Not Required States - Override old styles */
.options-wrapper .menu-item i.required {
  color: #10b981 !important;
  text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2) !important;
}

.options-wrapper .menu-item i.not-required {
  color: #64748b !important;
}

.options-wrapper .menu-item:hover i.required,
.options-wrapper .menu-item:hover i.not-required {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Enhanced Tooltips for Options Menu */
.options-wrapper .menu-item::before {
  content: attr(title);
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1001;
  pointer-events: none;
}

.options-wrapper .menu-item::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid #1f2937;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1001;
}

.options-wrapper .menu-item:hover::before,
.options-wrapper .menu-item:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Enhanced Specific Icon Styling for Options Menu */
.options-wrapper .menu-item .fa-comment {
  color: #3b82f6 !important;
}

.options-wrapper .menu-item .fa-image {
  color: #8b5cf6 !important;
}

.options-wrapper .menu-item .fa-flag {
  color: #f59e0b !important;
}

.options-wrapper .menu-item .fa-comment.required {
  color: #10b981 !important;
}

.options-wrapper .menu-item .fa-image.required {
  color: #10b981 !important;
}

.options-wrapper .menu-item .fa-flag.required {
  color: #10b981 !important;
}

/* Enhanced Animation for state changes in Options Menu */
.options-wrapper .menu-item i.required {
  animation: pulse-green 2s infinite;
}

/* Mobile Responsive for Options Wrapper */
@media (max-width: 768px) {
  .options-wrapper .options-menu {
    gap: 6px !important;
    padding: 3px !important;
    top: -45px !important;
    right: -15px !important;
  }

  .options-wrapper .menu-item {
    width: 32px !important;
    height: 32px !important;
  }

  .options-wrapper .menu-item i {
    font-size: 12px !important;
  }

  .options-wrapper .menu-item::before {
    font-size: 10px !important;
    bottom: -28px !important;
  }
}
.delete-btn {
  color: #dc3545;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
}

.delete-btn:hover {
  background-color: #f8d7da;
}

/* Add Item Container */
.add-item-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 5px;
}

/* Add Section Container */
.add-section-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
  margin-bottom: 20px;
  padding-left: 10px;
}

@media (max-width: 768px) {
  .add-section-container {
    padding-left: 0;
    justify-content: center;
  }

  .add-item-container {
    justify-content: center;
  }
}

/* Form Actions */
.form-actions {
  margin-top: 0px;
  padding-top: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 576px) {
  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Button styles are now in global styles.css */

/* Dropdown Lists Styles */
.dropdown-lists {
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  padding: 8px;
  max-height: 250px;
  overflow-y: auto;
}

.dropdown-list-header {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

/* Search input styles */
.dropdown-search {
  margin-bottom: 8px;
}

.dropdown-search-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  font-size: 12px;
  background-color: white;
}

.dropdown-search-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.dropdown-list-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 160px;
  overflow-y: auto;
}

.dropdown-list-item {
  padding: 6px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.dropdown-list-item:hover {
  background-color: #e9ecef;
}

.no-lists-message {
  color: #999;
  font-style: italic;
  font-size: 12px;
  padding: 5px 0;
  text-align: center;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #4CAF50;
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

/* Element Type Modal Styles */
.element-type-modal {
  width: 400px;
  max-height: 80vh;
  border-radius: 4px;
}

.element-type-modal .modal-header {
  background-color: #333;
  padding: 6px 15px;
}

.element-type-modal .modal-header h3 {
  font-size: 16px;
  font-weight: normal;
}

.element-type-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0;
}

.element-type-item {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.element-type-header {
  display: flex;
  align-items: center;
  padding: 6px 15px;
  cursor: pointer;
  width: 100%;
}

.element-type-header:hover {
  background-color: #f5f5f5;
}

.element-type-item:last-child {
  border-bottom: none;
}

.element-type-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 16px;
}

.element-type-item:nth-child(1) .element-type-icon { color: #4CAF50; }
.element-type-item:nth-child(2) .element-type-icon { color: #2196F3; }
.element-type-item:nth-child(3) .element-type-icon { color: #9C27B0; }
.element-type-item:nth-child(4) .element-type-icon { color: #FF9800; }
.element-type-item:nth-child(5) .element-type-icon { color: #F44336; }
.element-type-item:nth-child(6) .element-type-icon { color: #795548; }
.element-type-item:nth-child(7) .element-type-icon { color: #607D8B; }
.element-type-item:nth-child(8) .element-type-icon { color: #009688; }
.element-type-item:nth-child(9) .element-type-icon { color: #673AB7; }
.element-type-item:nth-child(10) .element-type-icon { color: #FFC107; }
.element-type-item:nth-child(11) .element-type-icon { color: #3F51B5; }

.element-type-name {
  font-size: 14px;
  color: #333;
}

.element-type-url-input {
  padding: 10px 15px 15px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.guide-image-preview {
  margin: 10px 0;
  text-align: center;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.element-type-url-input input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.element-type-url-input input:focus {
  outline: none;
  border-color: #2196F3;
}

.btn-add-url {
  align-self: flex-end;
  padding: 6px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
}

.btn-add-url:hover {
  background-color: #45a049;
}

/* Form group styles are now in global styles.css */

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Form Builder Container with Sidebar Layout */


/* Sidebar Styles - Static Position */

.sidebar {
  width: 280px;
  background-color: #2c6ddd;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}


.sidebar-header {
  padding: 12px 15px;
  border-bottom: 1px solid #3f51b5;
  background-color: #283fc0;
  /* background: linear-gradient(212deg, #667eea 0%, #137fd4 100%); */
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.sidebar-header h3 {
  margin: 0;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.add-form-btn {
  background-color: #79abfe;
  color: white;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-form-btn:hover {
  background-color: #3367d6;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.nav-link {
  display: flex;
  align-items: center;
  padding: 5px 15px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.nav-text {
  flex: 1;
  font-size: 14px;
}

.form-header {
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}


.form-header:hover {
  background-color: #f8f9fa;
}

.form-header.active {
  background-color: #e3f2fd;
  border-left: 3px solid #4285F4;
}

.form-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-header i {
  color: #999;
  font-size: 10px;
}

.no-forms {
  padding: 15px;
  text-align: center;
  color: white;
  font-style: italic;
  font-size: 12px;
}

/* Main Content Area - Centered and Scrollable */
.main-content {
  flex: 1;
  height: auto;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
}



/* No Selection State */
.no-selection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding: 40px;
}

.no-selection p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

/* Form Building Area - Centered with max width */


/* Form header styling */
.form-building-area .form-header {
  margin: -20px -20px 0px -20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
}

/* Ensure form sections don't take full width */
.form-building-area .form-preview {
  width: 100%;
}

.form-building-area .form-section {
  margin-bottom: 20px;
}
.box-content{
  display: flex;
}
.validation-box{
  width: 20vw;
  height: 30vh;
  margin-left: 20px;
  border: 1px solid green;
}


/* Mobile Responsive for Compact Sidebar */
@media (max-width: 768px) {
  .form-builder-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .sidebar-header {
    padding: 10px 15px;
  }

  .sidebar-header h3 {
    font-size: 12px;
  }

  .forms-list {
    display: flex;
    overflow-x: auto;
    padding: 0 10px;
  }

  .form-item {
    min-width: 120px;
    border-right: 1px solid #f0f0f0;
    border-bottom: none;
  }

  .form-header {
    padding: 8px 12px;
    white-space: nowrap;
  }

  .main-content {
    width: 100%;
    height: calc(100vh - 200px);
    padding: 15px;
  }

  .form-building-area {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    padding: 15px;
  }

  .form-building-area .form-header {
    margin: -15px -15px 15px -15px;
    padding: 15px;
    border-radius: 0;
  }
}

/* Options Menu Styles - Enhanced Design */
.options-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.options-menu:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background: white;
  border: 1px solid transparent;
}

.menu-item:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.menu-item:hover i {
  color: white !important;
  transform: scale(1.1);
}

.menu-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.menu-item i {
  font-size: 14px;
  transition: all 0.2s ease;
}

/* Enhanced Required/Not Required States */
.menu-item i.required {
  color: #10b981;
  text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
}

.menu-item i.not-required {
  color: #64748b;
}

.menu-item:hover i.required,
.menu-item:hover i.not-required {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Tooltip Enhancement */
.menu-item::before {
  content: attr(title);
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  pointer-events: none;
}

.menu-item::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid #1f2937;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
}

.menu-item:hover::before,
.menu-item:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Specific Icon Styling */
.menu-item .fa-comment {
  color: #3b82f6;
}

.menu-item .fa-image {
  color: #8b5cf6;
}

.menu-item .fa-flag {
  color: #f59e0b;
}

.menu-item .fa-comment.required {
  color: #10b981;
}

.menu-item .fa-image.required {
  color: #10b981;
}

.menu-item .fa-flag.required {
  color: #10b981;
}

/* Animation for state changes */
.menu-item i.required {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

/* Mobile Responsive for Options Menu */
@media (max-width: 768px) {
  .options-menu {
    gap: 6px;
    padding: 3px;
  }

  .menu-item {
    width: 32px;
    height: 32px;
  }

  .menu-item i {
    font-size: 12px;
  }

  .menu-item::before {
    font-size: 10px;
    bottom: -28px;
  }
}

/* Validation Box Styles */
.validation-box {
  width: 300px;
  min-height: 400px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 120px);
  flex-shrink: 0;
}

.validation-box.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.validation-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  padding: 15px;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.validation-header h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
}

.selected-element-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.element-type-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.element-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.close-validation-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-validation-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.validation-content {
  padding: 20px;
}

.validation-group {
  margin-bottom: 20px;
}

.validation-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.validation-label input[type="checkbox"] {
  margin-right: 8px;
}

.validation-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.validation-input:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.validation-help {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  font-style: italic;
}

.email-validation-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  color: #1976d2;
  font-size: 13px;
}

.email-validation-info i {
  color: #2196f3;
  font-size: 14px;
}

.no-selection-content {
  text-align: center;
  color: #999;
  padding: 20px;
}

.no-selection-content i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ddd;
}

.no-selection-content h4 {
  margin: 0 0 10px 0;
  color: #666;
}

.no-selection-content p {
  margin: 0;
  font-size: 14px;
}
/* Enhanced Condition Popup Styles */
.condition-popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.condition-popup {
  background: #fff;
  border-radius: 12px;
  min-width: 450px;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.popup-content {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-label i {
  color: #667eea;
  width: 16px;
}

.form-select,
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #fff;
  box-sizing: border-box;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select:hover,
.form-input:hover {
  border-color: #b8c5ea;
}

.condition-preview {
  background: #f8f9ff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 10px;
  font-size: 14px;
}

.preview-text {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.preview-text strong {
  color: #333;
  background: #e8ecff;
  padding: 2px 6px;
  border-radius: 4px;
}

.operator {
  color: #667eea;
  font-weight: 600;
}

.popup-actions {
  padding: 20px 25px;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e1e5e9;
}

.btn-apply,
.btn-cancel {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-apply {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-apply:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-apply:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-cancel {
  background: #fff;
  color: #666;
  border: 2px solid #e1e5e9;
}

.btn-cancel:hover {
  background: #f8f9fa;
  border-color: #ccc;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .condition-popup {
    min-width: 90%;
    max-width: 95%;
    margin: 20px;
  }

  .popup-header {
    padding: 15px;
  }

  .popup-header h3 {
    font-size: 16px;
  }

  .popup-content {
    padding: 20px;
  }

  .popup-actions {
    padding: 15px 20px;
    flex-direction: column;
  }

  .btn-apply,
  .btn-cancel {
    width: 100%;
    justify-content: center;
  }
}


/* Mobile Responsive for Validation Box */
@media (max-width: 768px) {
  .box-content {
    flex-direction: column;
  }

  .form-building-area {
    max-width: 100%;
    margin-left: 0;
  }

  .validation-box {
    width: 100%;
    position: relative;
    top: 0;
    order: -1; /* Show validation box above form on mobile */
  }

  .validation-header {
    padding: 12px;
  }

  .validation-header h4 {
    font-size: 14px;
  }

  .validation-content {
    padding: 15px;
  }

  .validation-group {
    margin-bottom: 15px;
  }
}
