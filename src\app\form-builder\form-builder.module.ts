import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { FormBuilderRoutingModule } from './form-builder-routing.module';
import { FormBuilderComponent } from './Component/form-builder.component';
import { FormListComponent } from './Component/form-list.component';
import { FormSkeletonComponent } from '../SharedData/form-skeleton.component';
import { FormConfigComponent } from './Component/form-config.component';
import { AdvancedBuilderComponent } from './Component/advanced-builder.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { PanelBarModule } from '@progress/kendo-angular-layout';

@NgModule({
  declarations: [
    FormBuilderComponent,
    FormListComponent,
    FormConfigComponent,
    AdvancedBuilderComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FormBuilderRoutingModule,
    FormSkeletonComponent,
    DragDropModule,
    InputsModule,
    PanelBarModule
  ],
  exports: [

  ]
})
export class FormBuilderModule { }
