/* Main Container */
.company-settings-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}

/* Sidebar Styles */
/* Code in Style.css me hai */

/* Content Cards */
.integration-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.integration-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.card-content {
   padding: 2.2vw;
}

.placeholder-text1 {
  color: #64748b;
  font-style: italic;
  text-align: center;
  padding: 40px 20px;
  margin: 0;
}

/* Integration Card Specific Styles */
.card-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.card-title {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.microsoft-logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.microsoft-logo i {
  font-size: 20px;
  color: white;
}

.title-text h3 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
}

.title-text p {
  margin: 0;
  color: #64748b;
  font-size: 0.95rem;
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  color: #334155;
}

.edit-button i {
  font-size: 14px;
}

/* Form Styles */
.integration-form {
  padding: 0 24px 24px 24px;
}

.form-grid-intrigration {
  display: grid;
  gap: 24px;
  margin-bottom: 32px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.form-label i {
  font-size: 14px;
  color: #667eea;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: #ffffff;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.save-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.save-button:active {
  transform: translateY(0);
}

/* Integration Display Styles */
.integration-display {
  padding: 0 24px 24px 24px;
}

.info-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.info-label i {
  font-size: 14px;
  color: #667eea;
}

.info-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.value-text {
  flex: 1;
  font-size: 14px;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  word-break: break-all;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.copy-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.copy-button i {
  font-size: 12px;
}

/* Status Indicators */
.status-indicator {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.status-badge.connected {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.disconnected {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-badge i {
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .company-settings-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .main-content {
    padding: 20px;
  }

  .content-section {
    max-width: 100%;
  }

  .section-title h2 {
    font-size: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .edit-button {
    align-self: flex-end;
  }
}

/* Company Form Styles */
.form-section {
  margin-bottom: 32px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
}

.section-icon {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  padding: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.field-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.field-input {
  padding: 12px 16px;
  border: none;
  border-bottom: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: #ffffff;
}

.field-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-input::placeholder {
  color: #9ca3af;
}

.error-message {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0 24px 24px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.save-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.cancel-button {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .form-field.full-width {
    grid-column: 1;
  }

  .section-header {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    padding: 16px;
  }

  .save-button,
  .cancel-button {
    width: 100%;
    justify-content: center;
  }
}

