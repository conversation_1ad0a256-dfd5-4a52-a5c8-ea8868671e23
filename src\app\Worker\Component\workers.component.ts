import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { DataStateChangeEvent, GridDataResult } from '@progress/kendo-angular-grid';
import { orderBy,process, SortDescriptor, State } from '@progress/kendo-data-query';
import { workers, workersData } from '../Models/Worker-Model';
import { CoreDataService } from '../../core-data.service';

@Component({
  selector: 'app-workers',
  templateUrl: './../Template/workers.component.html',
  styleUrl: './../Styles/workers.component.css'
})
export class WorkersComponent {
constructor(
private router:Router,
private coreData:CoreDataService,
){this.allData = this.allData.bind(this);}
gridHeight: number = 0;
screenSize: number = 0;
public Workers: workersData[] = [];
public Filteredlist: workersData[] = [];
public filteredWorkersList: workersData[] = [];
public state: State = {
  skip: 0,
  take: 40,
  sort: [],
};
public gridData: GridDataResult = process(this.Workers, this.state);

ngOnInit(): void {
  this.screenSize = window.innerHeight;
  this.gridHeight = this.screenSize - 60;
  this.Locations();
}

public dataStateChange(state: DataStateChangeEvent): void {
  this.state = state;
  this.gridData = process(this.Filteredlist, this.state);
}

public sortChange(sort: SortDescriptor[]): void {
  this.gridData = process(orderBy(this.Filteredlist, sort), this.state);
}

onCellClick(e:any){
  let rowIndex;
  rowIndex = e.target.parentElement.rowIndex;
  let rowData = this.gridData.data[e.target.parentElement.rowIndex];
  this.router.navigate(['Users/Details',rowData.id]);
}

Locations(){

this.coreData.GetUsers().subscribe({
  next:(users:workers[])=>{
    console.log('users from API' , users);
    const userList = users.map(item=>{
      return{
        id: item.id,
        ...item.details,
      };
    });
    this.Workers = userList;

    this.Filteredlist = userList;
    this.gridData = {
        data: this.Workers.slice(this.state.skip, this.state.take),
        total: this.Workers.length,
    };
  },
   error: (err) => {
    console.error('Error fetching location data:', err);
  }
});

}


public allData(): ExcelExportData {
  const result: ExcelExportData = {
    data: process(this.Workers, {
      filter: this.state.filter,
      sort: this.state.sort,
    }).data,
  };
  return result;
}

onLocation(){
  this.router.navigate(['Users/Details']);
}

}
