{"name": "form", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@azure/msal-angular": "^4.0.12", "@azure/msal-browser": "^4.12.0", "@progress/kendo-angular-buttons": "^19.1.0", "@progress/kendo-angular-grid": "^19.1.0", "@progress/kendo-angular-label": "^19.1.0", "@progress/kendo-svg-icons": "^4.3.0", "@progress/kendo-theme-material": "^11.0.2", "@types/google.maps": "^3.58.1", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "@zxing/ngx-scanner": "^19.0.0", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "form": "file:", "fuse.js": "^7.1.0", "html2pdf.js": "^0.10.3", "ng2-charts": "^4.1.1", "ngx-toastr": "^19.0.0", "pdf-lib": "^1.17.1", "rxjs": "~7.8.0", "signature_pad": "^5.0.7", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.0", "@angular/localize": "^18.2.13", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.21", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.5.2"}}