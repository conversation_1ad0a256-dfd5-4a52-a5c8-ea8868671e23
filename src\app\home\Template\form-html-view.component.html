<div class="form-html-view-container">

  <div class="form-content" #formContent>
    <!-- Header with logo and title -->
    <div class="form-header">
      <div class="logo-container">
        <img src="assets/Images/Airmaster-Header-Logo.png" alt="Airmaster Logo" class="header-logo">
      </div>
      <div class="header-text">
        <h1 class="form-title">{{ formName }}</h1>
        <p class="form-subtitle">Submitted by {{ userName }}</p>
      </div>
    </div>

    <!-- Form fields organized by section -->
    <div class="form-fields">
      <!-- Loop through each section -->
      <div *ngFor="let section of formSections" class="form-section">
        <!-- Section header -->
        <div class="section-header">
          <h3>{{ section.title }}</h3>
        </div>

        <!-- Section content -->
        <table class="form-table">
          <tbody>
            <ng-container *ngFor="let field of section.fields">
              <!-- Main field row -->
              <tr [ngClass]="{'image-row': field.type === 'image' || field.type === 'guidance' || field.type === 'signature'}">
                <td class="field-name">{{ field.label }}</td>
                <td class="field-value">
                  <!-- Display field value based on type -->
                  <ng-container [ngSwitch]="field.type">
                    <img *ngSwitchCase="'image'" [src]="getImageUrl(field.key)" class="form-image" alt="Image">
                    <img *ngSwitchCase="'signature'" [src]="field.value" class="signature-image" alt="Signature">
                    <img *ngSwitchCase="'guidance'" [src]="getImageUrl(field.key)" class="form-image" alt="Guidance">
                    <!-- <div *ngSwitchCase="'pdf'" class="pdf-link-container">
                      <a *ngIf="field.value" [href]="field.value" target="_blank" class="pdf-link">
                        <i class="fas fa-file-pdf pdf-icon"></i>
                        <span class="pdf-text">View PDF </span>
                        <i class="fas fa-external-link-alt external-icon"></i></a>
                    </div> -->
                    <span *ngSwitchDefault>{{ field.value }}</span>
                  </ng-container>
                </td>
              </tr>

              <!-- Display related fields (flag, comment, image) below the parent field -->
              <tr *ngFor="let relatedField of field.relatedFields" class="related-field-row">
                <td class="related-field-name">
                  <span class="indent">└─ {{ relatedField.label }}</span>
                </td>
                <td class="related-field-value">
                  <!-- Display related field value based on type -->
                  <ng-container [ngSwitch]="relatedField.type">
                    <img *ngSwitchCase="'image'" [src]="getImageUrl(relatedField.key)" class="related-image" alt="Related Image">
                    <span *ngSwitchCase="'flag'" class="flag-value">
                      <i class="fas fa-flag"></i> {{ relatedField.value }}
                    </span>
                    <span *ngSwitchCase="'comment'" class="comment-value">
                      <i class="fas fa-comment"></i> {{ relatedField.value }}
                    </span>
                    <!-- Follow-up form data display -->

                    <div *ngSwitchCase="'followUp'" class="followup-data">
                      <div class="followup-header">
                        <i class="fas fa-file-signature"></i>
                        <span>Follow-up Form Data</span>
                      </div>
                      <div class="followup-content">
                        <div *ngIf="isFollowUpDataObject(relatedField.value)" class="followup-form-structure">
                          <!-- Display follow-up form fields in a structured way -->
                          <div *ngFor="let followUpField of getFollowUpFields(relatedField.value)" class="followup-main-field">
                            <!-- Main field display -->
                            <div class="followup-field-row">
                              <span class="followup-field-label">{{ formatFieldLabel(followUpField.key) }}:</span>
                              <span class="followup-field-value" [ngClass]="'field-type-' + followUpField.type">
                                <!-- Handle different field types -->
                                <ng-container [ngSwitch]="followUpField.type">
                                  <img *ngSwitchCase="'signature'" [src]="followUpField.value" class="followup-signature-image" alt="Signature">
                                  <img *ngSwitchCase="'image'" [src]="getImageUrl(followUpField.key)" class="followup-image" alt="Image">
                                  <span *ngSwitchDefault>{{ followUpField.value }}</span>
                                </ng-container>
                              </span>
                            </div>

                            <!-- Related fields for this follow-up field (flags, comments, images, nested follow-ups) -->
                            <div *ngIf="followUpField.relatedFields && followUpField.relatedFields.length > 0" class="followup-related-fields">
                              <div *ngFor="let relatedSubField of followUpField.relatedFields" class="followup-related-field">
                                <span class="followup-related-label">└─ {{ relatedSubField.label }}:</span>
                                <span class="followup-related-value">
                                  <ng-container [ngSwitch]="relatedSubField.type">
                                    <img *ngSwitchCase="'image'" [src]="getImageUrl(relatedSubField.key)" class="followup-related-image" alt="Related Image">
                                    <span *ngSwitchCase="'flag'" class="followup-flag-value">
                                      <i class="fas fa-flag"></i> {{ relatedSubField.value }}
                                    </span>
                                    <span *ngSwitchCase="'comment'" class="followup-comment-value">
                                      <i class="fas fa-comment"></i> {{ relatedSubField.value }}
                                    </span>
                                    <!-- Nested follow-up forms (recursive) -->
                                    <div *ngSwitchCase="'followUp'" class="followup-nested">
                                      <div class="followup-nested-header">
                                        <i class="fas fa-layer-group"></i>
                                        <span>Nested Follow-up Form</span>
                                      </div>
                                      <div class="followup-nested-content">
                                        <div *ngFor="let nestedField of getFollowUpFields(relatedSubField.value)" class="followup-nested-field">
                                          <span class="followup-nested-label">{{ formatFieldLabel(nestedField.key) }}:</span>
                                          <span class="followup-nested-value">{{ nestedField.value }}</span>
                                        </div>
                                      </div>
                                    </div>
                                    <span *ngSwitchDefault>{{ relatedSubField.value }}</span>
                                  </ng-container>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div *ngIf="!isFollowUpDataObject(relatedField.value)" class="followup-simple">
                          {{ relatedField.value }}
                        </div>
                      </div>
                    </div>


                    <span *ngSwitchDefault>{{ relatedField.value }}</span>
                  </ng-container>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Signature section at the bottom if not already included -->
    <div class="signature-section" *ngIf="signatureImage && !hasSignatureField()">
      <h3>Signature</h3>
      <img [src]="signatureImage" alt="Signature" class="signature-image">
    </div>

    <!-- Manager Signatures section -->
    <div class="manager-signatures-section" *ngIf="managerSignatures && managerSignatures.length > 0">
      <h3>Manager Approvals ({{ managerSignatures.length }})</h3>
      <div class="manager-signatures-grid">
        <div class="manager-signature-item" *ngFor="let signature of managerSignatures; let i = index">
          <div class="manager-signature-header">
            <h4>{{ signature.name }}</h4>
            <p class="signature-timestamp">{{ signature.timestamp }}</p>
          </div>
          <div class="manager-signature-display">
            <img [src]="signature.signature" alt="Manager Signature" class="manager-signature-image" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
