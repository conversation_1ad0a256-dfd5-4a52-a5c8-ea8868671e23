<div class="form-html-view-container">

  <div class="form-content" #formContent>
    <!-- Header with logo and title -->
    <div class="form-header">
      <div class="logo-container">
        <img src="assets/Images/Airmaster-Header-Logo.png" alt="Airmaster Logo" class="header-logo">
      </div>
      <div class="header-text">
        <h1 class="form-title">{{ formName }}</h1>
        <p class="form-subtitle">Submitted by {{ userName }}</p>
      </div>
    </div>

    <!-- Form fields organized by section -->
    <div class="form-fields">
      <!-- Loop through each section -->
      <div *ngFor="let section of formSections" class="form-section">
        <!-- Section header -->
        <div class="section-header">
          <h3>{{ section.title }}</h3>
        </div>

        <!-- Section content -->
        <table class="form-table">
          <tbody>
            <ng-container *ngFor="let field of section.fields">
              <!-- Main field row -->
              <tr [ngClass]="{'image-row': field.type === 'image' || field.type === 'guidance' || field.type === 'signature'}">
                <td class="field-name">{{ field.label }}</td>
                <td class="field-value">
                  <!-- Display field value based on type -->
                  <ng-container [ngSwitch]="field.type">
                    <img *ngSwitchCase="'image'" [src]="getImageUrl(field.key)" class="form-image" alt="Image">
                    <img *ngSwitchCase="'signature'" [src]="field.value" class="signature-image" alt="Signature">
                    <img *ngSwitchCase="'guidance'" [src]="getImageUrl(field.key)" class="form-image" alt="Guidance">
                    <!-- <div *ngSwitchCase="'pdf'" class="pdf-link-container">
                      <a *ngIf="field.value" [href]="field.value" target="_blank" class="pdf-link">
                        <i class="fas fa-file-pdf pdf-icon"></i>
                        <span class="pdf-text">View PDF </span>
                        <i class="fas fa-external-link-alt external-icon"></i></a>
                    </div> -->
                    <span *ngSwitchDefault>{{ field.value }}</span>
                  </ng-container>
                </td>
              </tr>

              <!-- Display related fields (flag, comment, image) below the parent field -->
              <tr *ngFor="let relatedField of field.relatedFields" class="related-field-row">
                <td class="related-field-name">
                  <span class="indent">└─ {{ relatedField.label }}</span>
                </td>
                <td class="related-field-value">
                  <!-- Display related field value based on type -->
                  <ng-container [ngSwitch]="relatedField.type">
                    <img *ngSwitchCase="'image'" [src]="getImageUrl(relatedField.key)" class="related-image" alt="Related Image">
                    <span *ngSwitchCase="'flag'" class="flag-value">{{ relatedField.value }}</span>
                    <span *ngSwitchCase="'comment'" class="comment-value">{{ relatedField.value }}</span>
                    <span *ngSwitchDefault>{{ relatedField.value }}</span>
                  </ng-container>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Signature section at the bottom if not already included -->
    <div class="signature-section" *ngIf="signatureImage && !hasSignatureField()">
      <h3>Signature</h3>
      <img [src]="signatureImage" alt="Signature" class="signature-image">
    </div>

    <!-- Manager Signatures section -->
    <div class="manager-signatures-section" *ngIf="managerSignatures && managerSignatures.length > 0">
      <h3>Manager Approvals ({{ managerSignatures.length }})</h3>
      <div class="manager-signatures-grid">
        <div class="manager-signature-item" *ngFor="let signature of managerSignatures; let i = index">
          <div class="manager-signature-header">
            <h4>{{ signature.name }}</h4>
            <p class="signature-timestamp">{{ signature.timestamp }}</p>
          </div>
          <div class="manager-signature-display">
            <img [src]="signature.signature" alt="Manager Signature" class="manager-signature-image" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
