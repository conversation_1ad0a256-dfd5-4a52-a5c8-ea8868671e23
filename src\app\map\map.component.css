.map-container {
  width: 70%;
  height: 274px;
  position: relative;
  margin-bottom: 15px;
  margin-left: 5.5vw;
}

#map {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Add a helper message to guide users */
.map-container::after {
  content: "Click on the map or drag the marker to select a location";
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255,255,255,0.8);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 100;
}
.gm-style img[src*="closedhand_8_8.cur"] {
  display: none;
}
