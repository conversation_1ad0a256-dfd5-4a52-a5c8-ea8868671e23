<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Login</h2>
      <p>Welcome back! Please login to your account.</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">{{error}}</div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input 
          type="email" 
          formControlName="email" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" 
          placeholder="Enter your email"
        />
        <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
          <div *ngIf="f['email'].errors['required']">Email is required</div>
          <div *ngIf="f['email'].errors['email']">Please enter a valid email address</div>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input 
          type="password" 
          formControlName="password" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" 
          placeholder="Enter your password"
        />
        <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
          <div *ngIf="f['password'].errors['required']">Password is required</div>
          <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
        </div>
      </div>

      <div class="form-group form-check">
        <input type="checkbox" formControlName="rememberMe" class="form-check-input" id="rememberMe">
        <label class="form-check-label" for="rememberMe">Remember me</label>
      </div>

      <div class="form-group">
        <button [disabled]="loading" class="btn btn-primary btn-block">
          <span *ngIf="loading" class="spinner-border spinner-border-sm mr-1"></span>
          Login
        </button>
      </div>

      <div class="auth-links">
        <a routerLink="/auth/forgot-password">Forgot Password?</a>
        <a routerLink="/auth/signup">Don't have an account? Sign up</a>
      </div>
    </form>
  </div>
</div>
