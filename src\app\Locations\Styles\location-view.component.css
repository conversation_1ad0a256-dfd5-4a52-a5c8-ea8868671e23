.location-view-container{
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}
/* Sidebar Styles */
/* in STYLE.CSS ALL cODE IS pASTED */

/* New Form Styles - Matching the image design */
.details-section {
  padding: 0;
}

.details-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1.5rem;
  padding-bottom: 0.4rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-field {
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.2rem;
  min-height: 36px;
}

.detail-field:last-of-type {
  margin-bottom: 1.5rem;
}

.field-label {
  flex: 0 0 100px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #999e9e;
  padding-top: 0.5rem;
  padding-right: 1rem;
  line-height: 1.4;
}

.field-value {
  flex: 1;
  max-width: auto;
}

.detail-input {
  width: 100%;
  padding: 0.5rem 0.7rem;
  /* border: 1px solid #d1d5db; */
  border: none;
  border-radius: 5px;
  font-size: 0.85rem;
  color: #374151;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.detail-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.detail-input::placeholder {
  color: #9ca3af;
}

/* Error States */
.detail-input.error,
.detail-datepicker.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.detail-input.error:focus,
.detail-datepicker.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Error Messages */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.3rem;
  display: flex;
  align-items: center;
  animation: slideInError 0.2s ease-out;
}

.error-message::before {
  content: "⚠";
  margin-right: 0.3rem;
  font-size: 0.8rem;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Date Fields Row */
.date-fields-row {
  display: flex;
  gap: 15rem;
  align-items: flex-start;
}

.date-field-group {
  display: flex;
  align-items: flex-start;
  min-width: 160px;
  gap: 3vw;
}

.date-field-group .field-label {
  flex: 0 0 70px;
  padding-right: 0.8rem;
}

.date-field-group .field-value {
  flex: 1;
}

.detail-datepicker {
  width: 100%;
  padding: 0.5rem 0.7rem;
  border: none;
  border-radius: 5px;
  font-size: 0.85rem;
  color: #374151;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.detail-datepicker:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 0.8rem;
  margin-top: 1.8rem;
  padding-top: 1.2rem;
  /* border-top: 1px solid #e5e7eb; */
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.save-button {
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 5px;
  background-color: #3b82f6;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-family: inherit;
}

.save-button:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.save-button:active {
  transform: translateY(0);
}

.cancel-button {
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 5px;
  background-color: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-family: inherit;
}

.cancel-button:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.cancel-button:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .detail-field {
    flex-direction: column;
    align-items: stretch;
  }

  .field-label {
    flex: none;
    padding-right: 0;
    padding-bottom: 0.5rem;
    padding-top: 0;
  }

  .date-fields-row {
    flex-direction: column;
    gap: 1.5rem;
  }

  .date-field-group {
    flex-direction: column;
    min-width: auto;
  }

  .date-field-group .field-label {
    flex: none;
    padding-right: 0;
    padding-bottom: 0.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .save-button,
  .cancel-button {
    width: 100%;
  }

  /* Error message responsive styles */
  .error-message {
    font-size: 0.7rem;
    margin-top: 0.2rem;
  }

  .error-message::before {
    font-size: 0.7rem;
    margin-right: 0.2rem;
  }
}

/* Location User Management Styles */
.loc-user-management-section {
  padding: 0;
}

.loc-user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.loc-user-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loc-user-title i {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.loc-user-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.loc-user-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.loc-add-user-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loc-add-user-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.loc-add-user-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loc-loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #6b7280;
  font-size: 14px;
}

.loc-loading-state i {
  font-size: 18px;
  color: #667eea;
}

.loc-users-container {
  min-height: 200px;
}

.loc-no-users {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loc-no-users i {
  font-size: 35px;
  color: #d1d5db;
  margin-bottom:5px;
}

.loc-no-users h4 {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.loc-no-users p {
  font-size: 14px;
  margin: 0 0 24px 0;
  color: #6b7280;
}

.loc-add-first-user-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.loc-add-first-user-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.loc-add-first-user-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loc-users-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 4px;
  justify-content: flex-start;
}

/* When there are 3 or more cards, use grid for even distribution */
.loc-users-grid:has(.loc-user-card:nth-child(3)) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 8px;
}

/* Alternative approach for browsers that don't support :has() */
@media (min-width: 1200px) {
  .loc-users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px;
  }

  /* Override for 1-2 cards to use flex */
  .loc-users-grid:not(:has(.loc-user-card:nth-child(3))) {
    display: flex;
    justify-content: flex-start;
  }
}

@media (min-width: 900px) and (max-width: 1199px) {
  .loc-users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px;
  }

  /* Override for 1-2 cards to use flex */
  .loc-users-grid:not(:has(.loc-user-card:nth-child(3))) {
    display: flex;
    justify-content: flex-start;
  }
}

.loc-user-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  width:20vw;
  padding: 20px;
  position: relative;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 20vw;
  min-width: 280px;
  max-width: 350px;
  cursor: pointer;
}

.loc-user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.loc-user-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.loc-user-avatar i {
  font-size: 20px;
  color: white;
}

.loc-user-info {
  flex: 1;
}

.loc-user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.loc-user-job {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.loc-user-email {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.loc-remove-user-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.loc-remove-user-btn:hover {
  background: #fecaca;
  color: #b91c1c;
  transform: scale(1.1);
}

/* Modal Styles */
.loc-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.loc-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.3s ease;
}

.loc-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.loc-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.loc-modal-close {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loc-modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.loc-modal-body {
  padding: 24px;
}

.loc-select-user-section {
  margin-bottom: 20px;
}

.loc-select-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.loc-user-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  color: #1f2937;
  background: white;
  transition: border-color 0.2s ease;
}

.loc-user-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.loc-no-available-users {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
}

.loc-no-available-users i {
  font-size: 16px;
}

.loc-no-available-users p {
  margin: 0;
  font-size: 14px;
}

.loc-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.loc-modal-cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.loc-modal-cancel-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.loc-modal-add-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loc-modal-add-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.loc-modal-add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive for User Management */
@media (max-width: 768px) {
  .loc-user-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .loc-users-grid {
    display: flex !important;
    flex-direction: column;
    gap: 8px;
  }

  .loc-user-card {
    padding: 16px;
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
  }

  .loc-modal-content {
    width: 95%;
    margin: 20px;
  }

  .loc-modal-header,
  .loc-modal-body,
  .loc-modal-footer {
    padding: 16px;
  }

  .loc-modal-footer {
    flex-direction: column;
  }

  .loc-modal-cancel-btn,
  .loc-modal-add-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 769px) and (max-width: 899px) {
  .loc-users-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 8px;
  }

  .loc-user-card {
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
  }
}

/* Dynamic grid classes for different card counts */
.loc-users-grid-flex {
  display: flex !important;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
}

.loc-users-grid-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 8px;
}

/* Ensure cards in flex layout don't stretch */
.loc-users-grid-flex .loc-user-card {
  flex: 0 0 auto;
}
