/* Advanced Form Builder Styles */
.advanced-form-builder {
  display: flex;
  height: 100vh;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Left Sidebar - Form Elements */
.form-elements-sidebar {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.elements-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.element-group {
  padding: 8px 0;
}

.element-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  margin: 4px 0;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #ffffff;
  cursor: grab;
  transition: all 0.2s ease;
  font-size: 13px;
}

.element-item:hover {
  background: #f0f7ff;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.element-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.element-icon {
  font-size: 14px;
  color: #666;
  width: 16px;
  text-align: center;
}

.element-label {
  flex: 1;
  font-weight: 500;
  color: #333;
}

/* Kendo PanelBar Customization */
.k-panelbar .k-panelbar-item > .k-link {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 13px;
  color: #333;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.k-panelbar .k-panelbar-item > .k-link:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.k-panelbar .k-panelbar-item.k-expanded > .k-link {
  background: #e3f2fd;
  color: #1976d2;
}

/* Form Canvas */
.form-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  margin: 0 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #ffffff;
  border-radius: 8px 8px 0 0;
}

.canvas-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.canvas-actions {
  display: flex;
  gap: 8px;
}

.canvas-drop-zone {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  min-height: 400px;
}

.empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.empty-canvas-content {
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
  display: block;
}

.empty-canvas-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.empty-canvas-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Form Field Items */
.form-field-item {
  margin-bottom: 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.form-field-item:hover {
  border-color: #2196f3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.form-field-item.selected {
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 6px 6px 0 0;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-type-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.field-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.required-indicator {
  color: #f44336;
  font-weight: bold;
  font-size: 16px;
}

.field-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.form-field-item:hover .field-actions {
  opacity: 1;
}

.field-preview {
  padding: 16px;
}

.preview-content {
  margin-bottom: 8px;
}

.preview-content:last-child {
  margin-bottom: 0;
}

.signature-preview,
.upload-preview,
.generic-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px dashed #ccc;
  border-radius: 6px;
  background: #f8f9fa;
  color: #666;
  font-size: 14px;
}

/* Properties Panel */
.properties-panel {
  width: 320px;
  background: #ffffff;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
}

.properties-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.properties-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.properties-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;
}

.property-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 4px;
}

.property-group {
  margin-bottom: 16px;
}

.property-group:last-child {
  margin-bottom: 0;
}

.property-group kendo-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #555;
}

.property-group kendo-textbox,
.property-group kendo-textarea,
.property-group kendo-dropdownlist,
.property-group kendo-numerictextbox {
  width: 100%;
}

.k-checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  padding: 4px 0;
}

.k-checkbox-label:hover {
  color: #1976d2;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.no-selection-content {
  text-align: center;
  color: #666;
}

.no-selection-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
  display: block;
}

.no-selection-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.no-selection-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Drag and Drop Styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background: #ffffff;
  border: 2px solid #2196f3;
  opacity: 0.9;
  transform: rotate(2deg);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #f0f7ff;
  border: 2px dashed #2196f3;
  border-radius: 6px;
}

.cdk-drop-list-dragging .form-field-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.canvas-drop-zone.cdk-drop-list-receiving {
  background: #f0f7ff;
  border: 2px dashed #2196f3;
  border-radius: 8px;
}

/* Kendo UI Component Customizations */
.k-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.k-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.k-textbox,
.k-textarea,
.k-dropdownlist {
  border-radius: 6px;
  border-color: #e0e0e0;
  transition: all 0.2s ease;
}

.k-textbox:focus,
.k-textarea:focus,
.k-dropdownlist:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.k-panelbar {
  border: none;
}

.k-panelbar .k-panelbar-item {
  border: none;
  border-radius: 6px;
  margin-bottom: 4px;
  overflow: hidden;
}

.k-panelbar .k-panelbar-item > .k-link {
  border-radius: 6px;
  transition: all 0.2s ease;
}

/* Scrollbar Styling */
.elements-container::-webkit-scrollbar,
.canvas-drop-zone::-webkit-scrollbar,
.properties-content::-webkit-scrollbar {
  width: 6px;
}

.elements-container::-webkit-scrollbar-track,
.canvas-drop-zone::-webkit-scrollbar-track,
.properties-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.elements-container::-webkit-scrollbar-thumb,
.canvas-drop-zone::-webkit-scrollbar-thumb,
.properties-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.elements-container::-webkit-scrollbar-thumb:hover,
.canvas-drop-zone::-webkit-scrollbar-thumb:hover,
.properties-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-elements-sidebar {
    width: 240px;
  }

  .properties-panel {
    width: 280px;
  }
}

@media (max-width: 992px) {
  .advanced-form-builder {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .form-elements-sidebar,
  .properties-panel {
    width: 100%;
    max-height: 300px;
  }

  .form-canvas {
    margin: 8px 0;
    min-height: 500px;
  }

  .canvas-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .canvas-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .sidebar-header,
  .properties-header {
    padding: 12px;
  }

  .sidebar-title,
  .properties-title {
    font-size: 14px;
  }

  .canvas-header {
    padding: 12px 16px;
  }

  .canvas-title {
    font-size: 16px;
  }

  .canvas-drop-zone {
    padding: 16px;
  }

  .field-header {
    padding: 8px 12px;
  }

  .field-preview {
    padding: 12px;
  }

  .properties-content {
    padding: 12px;
  }
}

/* Animation Classes */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.form-elements-sidebar {
  animation: slideInLeft 0.3s ease-out;
}

.properties-panel {
  animation: slideInRight 0.3s ease-out;
}

.form-field-item {
  animation: fadeInUp 0.3s ease-out;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.element-item:focus,
.form-field-item:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

.k-button:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .advanced-form-builder {
    background: #ffffff;
  }

  .form-elements-sidebar,
  .form-canvas,
  .properties-panel {
    border-color: #000000;
  }

  .element-item,
  .form-field-item {
    border-color: #000000;
  }

  .element-item:hover,
  .form-field-item:hover {
    background: #000000;
    color: #ffffff;
  }
}
