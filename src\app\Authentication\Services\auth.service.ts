import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthResponse, LoginRequest, SignupRequest, User, ResetPasswordRequest, ChangePasswordRequest } from '../Model/auth.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private baseUrl: string = "";
  private authUrl = 'api/auth';
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser$: Observable<User | null>;
  private tokenExpirationTimer: any;

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    // Initialize the user from localStorage if available
    const storedUser = localStorage.getItem('currentUser');
    this.currentUserSubject = new BehaviorSubject<User | null>(storedUser ? JSON.parse(storedUser) : null);
    this.currentUser$ = this.currentUserSubject.asObservable();

    // Set up the base URL
    if (window.location.origin.includes("localhost")) {
      this.baseUrl = "https://revauo.eudemonic.co.in/api/";
    } else {
      this.baseUrl = window.location.origin + "/api/";
    }
    this.authUrl = this.baseUrl + this.authUrl;
  }

  // Get the current user value
  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  // Check if user is logged in
  public get isLoggedIn(): boolean {
    return !!this.currentUserSubject.value && !!localStorage.getItem('token');
  }

  // Login
  login(credentials: LoginRequest): Observable<AuthResponse> {
    // Hardcoded login for testing purposes
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      console.log('Using hardcoded login credentials for testing');

      // Create a mock successful response
      const mockResponse: AuthResponse = {
        success: true,
        token: 'mock-jwt-token-for-testing-purposes-' + new Date().getTime(),
        user: {
          id: '1',
          firstName: 'Dev One',
          lastName: 'One',
          email: credentials.email,
          role: 'user'
        }
      };

      // Set the session with our mock data
      this.setSession(mockResponse);

      // Return the mock response as an observable
      return of(mockResponse);
    }

    // If not using hardcoded credentials, proceed with actual API call
    // This is commented out for now since we're using hardcoded values
    /*
    return this.http.post<AuthResponse>(`${this.authUrl}/login`, credentials)
      .pipe(
        tap(response => {
          if (response && response.token) {
            this.setSession(response);
          }
        }),
        catchError(error => {
          console.error('Login error:', error);
          return throwError(() => new Error(error.error?.message || 'Login failed. Please try again.'));
        })
      );
    */

    // For any other credentials, return a mock error
    return throwError(() => new Error('Invalid credentials. Use <EMAIL> / password for testing.'));
  }

  // Register new user
  signup(userData: SignupRequest): Observable<AuthResponse> {
    // Hardcoded signup for testing purposes
    console.log('Using hardcoded signup for testing');

    // Validate password match
    if (userData.password !== userData.confirmPassword) {
      return throwError(() => new Error('Passwords do not match.'));
    }

    // Create a mock successful response
    const mockResponse: AuthResponse = {
      success: true,
      token: 'mock-jwt-token-for-testing-purposes-' + new Date().getTime(),
      user: {
        id: '1',
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        role: 'user'
      }
    };

    // Set the session with our mock data
    this.setSession(mockResponse);

    // Return the mock response as an observable
    return of(mockResponse);

    // If not using hardcoded signup, proceed with actual API call
    // This is commented out for now since we're using hardcoded values
    /*
    return this.http.post<AuthResponse>(`${this.authUrl}/register`, userData)
      .pipe(
        tap(response => {
          if (response && response.token) {
            this.setSession(response);
          }
        }),
        catchError(error => {
          console.error('Registration error:', error);
          return throwError(() => new Error(error.error?.message || 'Registration failed. Please try again.'));
        })
      );
    */
  }

  // Request password reset
  requestPasswordReset(email: ResetPasswordRequest): Observable<any> {
    // Hardcoded password reset for testing purposes
    console.log('Using hardcoded password reset for testing:', email);

    // Simulate successful password reset request
    if (email.email) {
      return of({
        success: true,
        message: 'Password reset instructions have been sent to your email.'
      });
    }

    // If email is empty, return an error
    return throwError(() => new Error('Email is required.'));

    // If not using hardcoded password reset, proceed with actual API call
    // This is commented out for now since we're using hardcoded values
    /*
    return this.http.post(`${this.authUrl}/forgot-password`, email)
      .pipe(
        catchError(error => {
          console.error('Password reset request error:', error);
          return throwError(() => new Error(error.error?.message || 'Password reset request failed. Please try again.'));
        })
      );
    */
  }

  // Change password
  changePassword(passwordData: ChangePasswordRequest): Observable<any> {
    return this.http.post(`${this.authUrl}/change-password`, passwordData)
      .pipe(
        catchError(error => {
          console.error('Change password error:', error);
          return throwError(() => new Error(error.error?.message || 'Password change failed. Please try again.'));
        })
      );
  }

  // Logout
  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('tokenExpiration');
    localStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
    if (this.tokenExpirationTimer) {
      clearTimeout(this.tokenExpirationTimer);
    }
    this.tokenExpirationTimer = null;
    this.router.navigate(['/auth/login']);
  }

  // Set session data
  private setSession(authResult: AuthResponse): void {
    // Store token and user data
    localStorage.setItem('token', authResult.token);
    localStorage.setItem('currentUser', JSON.stringify(authResult.user));

    // Set token expiration (assuming JWT with 1 day expiration)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 1);
    localStorage.setItem('tokenExpiration', expiresAt.toISOString());

    // Update the current user subject
    this.currentUserSubject.next(authResult.user);

    // Set auto logout timer
    this.autoLogout(expiresAt.getTime() - new Date().getTime());
  }

  // Auto logout when token expires
  autoLogout(expirationDuration: number): void {
    this.tokenExpirationTimer = setTimeout(() => {
      this.logout();
    }, expirationDuration);
  }

  // Check if token is expired
  isTokenExpired(): boolean {
    const expiration = localStorage.getItem('tokenExpiration');
    if (!expiration) return true;

    const expiresAt = new Date(expiration);
    return expiresAt <= new Date();
  }

  // Auto login on app initialization
  autoLogin(): Observable<boolean> {
    console.log('Attempting auto login...');

    const token = localStorage.getItem('token');
    const userJson = localStorage.getItem('currentUser');
    const expirationDate = localStorage.getItem('tokenExpiration');

    if (!token || !userJson || !expirationDate) {
      console.log('Auto login failed: Missing token, user data, or expiration date');
      return of(false);
    }

    const expiresAt = new Date(expirationDate);
    if (expiresAt <= new Date()) {
      console.log('Auto login failed: Token expired');
      this.logout();
      return of(false);
    }

    try {
      const user: User = JSON.parse(userJson);
      this.currentUserSubject.next(user);

      // Set auto logout timer
      const timeUntilExpiry = expiresAt.getTime() - new Date().getTime();
      console.log(`Auto login successful. Token expires in ${Math.round(timeUntilExpiry / 1000 / 60)} minutes`);
      this.autoLogout(timeUntilExpiry);

      return of(true);
    } catch (error) {
      console.error('Auto login failed: Error parsing user data', error);
      this.logout();
      return of(false);
    }
  }

  // Get auth token
  getToken(): string | null {
    return localStorage.getItem('token');
  }
}
