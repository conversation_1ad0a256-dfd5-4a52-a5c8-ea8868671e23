

export interface DateValidations {
  minDate?: string;  // Expected format: "YYYY-MM-DD"
  maxDate?: string;  // Expected format: "YYYY-MM-DD"
  noFuture?: boolean;
  noPast?: boolean;
}

export interface Style {
  [key: string]: string;
}

export interface FormJson {
  id?: string;
  auditHistory: {
    userName?: string;
    formName?: string;
    location?: string;
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
    userID?: string;
  };
  component: FormSection[];
}
export interface FormSection {
  title: string;
  canCollapsed?: boolean;
  isCollapsed?: boolean;
  repeatable?: boolean;
  condition?: {
      field: string;
      operator: '==' | '!=';
      value: any;
    };
    elements: FormComponent[];
    _isConditional?: boolean;
  }



export type FormComponent =
  | TextComponent
  | SelectComponent
  | MapComponent
  | TextareaComponent
  | fileComponent
  | ImageComponent
  | DateComponent
  | SignatureComponent
  | QRScannerComponent
  | LinkComponent
  | ViewPdfComponent
  | UploadPdfComponent
  | TimerComponent;


export interface TextComponent  {
  type: 'text';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    style?: Style;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;

  };
}

export interface SelectComponent  {
  type: 'Select';
  multiselect: boolean;
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    dataListId: string;
    dataSource?: DropdownData;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}


export interface MapComponent  {
  type: 'map';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;

    default_lat?: number;
    default_lng?: number;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface TextareaComponent  {
  type: 'textarea';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    placeholder_text?: string;
    default_value?: string;
    show_label?: boolean;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface fileComponent  {
  type: 'file';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    show_label?: boolean;
    placeholder_text?: string;
    default_value?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    imageDisplayUrl?: string;
    image_url?: string;
    validations?: any;

  };
}

export interface ImageComponent {
  type: 'image';
  attributes: {
    label: string;
    field_name?: string;
    is_required?: boolean;
    default_value?: string;
    show_label?: boolean;
    placeholder_text?: string;
    uploadedFileName?: string;
    imageDisplayUrl?: string;
    image_url?: string;
    alt_text?: string;
    actions?: ActionModel;
    validations?: any;
  };
}

export interface DateComponent  {
  type: 'date';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    default_value?: string;
    show_label?: boolean;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface SignatureComponent  {
  type: 'signature';
  attributes: {
    label: string;
    field_name: string;
    default_value?: string;
    is_required: boolean;
    show_label?: boolean;
    pen_color?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface QRScannerComponent {
  type: 'qrscanner';
  attributes: {
    label: string;
    field_name: string;
    placeholder_text?: string;
    default_value?: string;
    is_required: boolean;
    show_label?: boolean;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface LinkComponent  {
  type: 'link';
  attributes: {
    label: string;
    is_required?: boolean;
    field_name?: string;
    show_label?: boolean;
    default_value?: string;
    url: string;
    link_text: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface ViewPdfComponent  {
  type: 'pdfviewer';
  attributes: {
    label: string;
    field_name?: string;
    is_required: boolean;
    show_label?: boolean;
    placeholder_text?: string;
    default_value?: string;
    pdf_url?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface UploadPdfComponent  {
  type: 'PDFfile';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;
    placeholder_text?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface TimerComponent  {
  type: 'time';
  attributes: {
    label: string;
    field_name: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;
    placeholder_text?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}








































export interface DropdownData {
  id?: string;
  list: FormListData[];
  auditHistory: {
    userName?: string;
    listName?: string;
    location?: string;
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
    userID?: string;
  };
}

export interface FormListData {
  title?: string;
  items: ListValue[];
}

export interface ListValue {
  value: string;
}


export interface ActionModel {
  comment?: boolean; // Define the properties of ActionModeL
  camera?: boolean;
  flag?: boolean;
  followupForm?: {
    formId: string;
    formName: string;
  };
}

export interface FormSubmission {
  id?: string;
  formTemplateId?: string;
  auditHistory: {
    createdBy?: string;
    createdDate?: Date;
    updatedBy?: string;
    updatedDate?: Date;
    location?: string;
    status?: string;
    userID?: string;
  };
  formData: any;
}

