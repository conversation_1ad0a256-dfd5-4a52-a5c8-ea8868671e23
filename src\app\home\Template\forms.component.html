<!-- Mobile View -->
<div *ngIf="isMobileView" class="mobile-container">

  <!-- Mobile Tab Content -->
  <div class="mobile-tab-content">

    <!-- Forms Tab Content -->
    <div *ngIf="activeTab === 'forms'" class="tab-content-forms">

      <!-- Mobile Forms List -->
      <div *ngIf="mobileView === 'forms'" class="mobile-forms-view">
    <div class="mobile-header">
      <h3>Select a Form</h3>
    </div>
    <div class="mobile-content">
      <div class="mobile-forms-grid">
        <div *ngFor="let form of this.shareService.FromList"
             class="mobile-form-card"
             (click)="selectFormForMobile(form)">
          <div class="mobile-form-info">
            <h4>{{ form.auditHistory.formName || 'Unnamed Form' }}</h4>
            <p class="form-location">{{ form.auditHistory.location || 'No location' }}</p>
            <p class="form-date">{{ form.auditHistory.createdDate | date:'short' }}</p>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      <div *ngIf="this.shareService.FromList.length === 0" class="mobile-no-data">
        <i class="fas fa-file-alt"></i>
        <p>No forms available</p>
      </div>
    </div>
  </div>

  <!-- Mobile Form Options (New/Previous) -->
  <div *ngIf="mobileView === 'options'" class="mobile-options-view">
    <div class="mobile-header">
      <button class="mobile-back-btn" (click)="backToMobileForms()">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h3>{{ selectedMobileForm?.auditHistory?.formName || 'Form Options' }}</h3>
    </div>
    <div class="mobile-content">
      <div class="mobile-options-grid">
        <div class="mobile-option-card new-option" (click)="selectNewForm()">
          <div class="option-icon">
            <i class="fas fa-plus-circle"></i>
          </div>
          <div class="option-content">
            <h4>New Submission</h4>
            <p>Create a new form submission</p>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>

        <div class="mobile-option-card previous-option" (click)="selectPreviousSubmissions()">
          <div class="option-icon">
            <i class="fas fa-history"></i>
          </div>
          <div class="option-content">
            <h4>Previous Submissions</h4>
            <p>View previous submissions</p>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Submissions List -->
  <div *ngIf="mobileView === 'submissions'" class="mobile-submissions-view">
    <div class="mobile-header">
      <button class="mobile-back-btn" (click)="backToMobileOptions()">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h3>Previous Submissions</h3>
    </div>
    <div class="mobile-content">
      <div class="mobile-submissions-grid">
        <div *ngFor="let submission of formSubmissions"
             class="mobile-submission-card"
             (click)="selectMobileSubmission(submission)">
          <div class="mobile-submission-info">
            <h4>{{ submission.auditHistory.updatedDate | date:'medium' }}</h4>
            <p class="submission-by">By: {{ submission.auditHistory.updatedBy || 'Dev One' }}</p>
            <p class="submission-location">{{ submission.auditHistory.location || 'No location' }}</p>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      <div *ngIf="formSubmissions.length === 0" class="mobile-no-data">
        <i class="fas fa-info-circle"></i>
        <p>No previous submissions found</p>
      </div>
    </div>
  </div>

  <!-- Mobile Form Render -->
  <div *ngIf="mobileView === 'form'" class="mobile-form-view">
    <div class="mobile-header">
      <button class="mobile-back-btn" (click)="backFromMobileForm()">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h3>{{ selectedMobileForm?.auditHistory?.formName || 'Form' }}</h3>
    </div>
    <div class="mobile-form-content">
      <app-home
        [formId]="selectedFormId || ''"
        [submissionId]="selectedSubmissionId || ''">
      </app-home>
    </div>
    </div>

    </div>

    <!-- Location Tab Content -->
    <div *ngIf="activeTab === 'location'" class="tab-content-location">
      <div class="mobile-header">
        <h3>Locations</h3>
      </div>
      <div class="mobile-content">
        <div class="mobile-locations-grid">
          <div *ngFor="let location of uniqueLocations"
               class="mobile-location-card"
               (click)="selectLocation(location)">
            <div class="mobile-location-info">
              <h4>{{ location }}</h4>
              <p class="location-forms-count">{{ getFormsCountByLocation(location) }} forms</p>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        <div *ngIf="uniqueLocations.length === 0" class="mobile-no-data">
          <i class="fas fa-map-marker-alt"></i>
          <p>No locations available</p>
        </div>
      </div>
    </div>

  </div>

  <!-- Bottom Tab Navigation -->
  <div class="mobile-bottom-tabs">
    <button class="tab-button"
            [class.active]="activeTab === 'forms'"
            (click)="switchTab('forms')">
      <i class="fas fa-file-alt"></i>
      <span>Forms</span>
    </button>
    <button class="tab-button"
            [class.active]="activeTab === 'location'"
            (click)="switchTab('location')">
      <i class="fas fa-map-marker-alt"></i>
      <span>Location</span>
    </button>
  </div>

</div>

<!-- Desktop View -->
<div *ngIf="!isMobileView" class="forms-container">
  <!-- Sidebar -->
  <div class="sidebar" [class.collapsed]="isSidebarCollapsed">
    <!-- Forms List View -->
    <div *ngIf="sidebarView === 'forms'">
      <div class="sidebar-header">
        <h3>Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="forms-list">
          <li *ngFor="let form of this.shareService.FromList" class="form-item">
            <div class="form-header"
                 [class.active]="selectedFormId === form.id"
                 (click)="selectForm(form)">
              <span class="form-name">{{ form.auditHistory.formName || 'Unnamed Form' }}</span>
              <i class="fas fa-chevron-right"></i>
            </div>
          </li>
        </ul>
        <div *ngIf="this.shareService.FromList.length === 0" class="no-forms">
          No forms available
        </div>
      </div>
    </div>

    <!-- Form Types View -->
    <div *ngIf="sidebarView === 'types'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToForms()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>{{ currentFormForTypes?.auditHistory?.formName || 'Form Types' }}</h3>
      </div>
      <div class="sidebar-content">
        <ul class="form-types-list">
          <li *ngFor="let type of formTypes"
              class="form-type-item"
              (click)="selectFormType(type)">
            <span class="form-type-name">{{ type }}</span>
            <i class="fas fa-chevron-right" style="color: lightgray;"></i>
          </li>
        </ul>
      </div>
    </div>

    <!-- Form Submissions View -->
    <div *ngIf="sidebarView === 'submissions'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToFormTypes()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>Submitted Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="submissions-list">
          <li *ngFor="let submission of formSubmissions"
              class="submission-item"
              [class.active]="selectedSubmissionId === submission.id"
              (click)="selectSubmission(submission)">
            <div class="submission-header">
              <!-- <span class="submission-id">ID: {{ submission.id }}</span> -->
              <span class="submission-date">{{ submission.auditHistory.updatedDate | date:'short' }}</span>
            </div>
            <div class="submission-info">
              <span class="submission-by">By: {{ submission.auditHistory.updatedBy || 'Dev One' }}</span>
            </div>
          </li>
        </ul>
        <div *ngIf="formSubmissions.length === 0" class="no-submissions">
          <i class="fas fa-info-circle"></i>
          No data submitted for this form
        </div>
      </div>
    </div>

  </div>

  <!-- Main Content -->
  <div class="main-content" [class.expanded]="isSidebarCollapsed">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" (click)="toggleSidebar()">
      <i class="fas" [ngClass]="{'fa-chevron-right': isSidebarCollapsed, 'fa-chevron-left': !isSidebarCollapsed}"></i>
    </button>

    <!-- Form Content -->
    <ng-container >
      <div *ngIf="selectedFormId" class="form-details">
        <!-- Pass both formId and submissionId (if available) to the home component -->
        <app-home
          [formId]="selectedFormId || ''"
          [submissionId]="selectedSubmissionId || ''">
        </app-home>
      </div>
      <div *ngIf="!selectedFormId" class="no-selection">
        <p>Select a form from the sidebar to view details</p>
      </div>
    </ng-container>

  </div>
</div>
