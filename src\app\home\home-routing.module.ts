import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormsComponent } from './Component/forms.component';
import { AuthGuard } from '../Authentication/Services/auth.guard';
import { HomeComponent } from './Component/home.component';
import { SharedFormViewComponent } from './Component/shared-form-view.component';
import { DashBoardComponent } from './Component/dash-board.component';

const routes: Routes = [
  {
    path: '',
    component: FormsComponent
  },
  {
    path: 'form-view',
    component: FormsComponent, // For direct HTML form view sharing
    // canActivate: [AuthGuard]
  },
  {
    path: 'admin',
    component: DashBoardComponent, // For Admin View
    // canActivate: [AuthGuard]
  },
  {
    path: 'form-preview',
    component: SharedFormViewComponent,
    // canActivate: [AuthGuard]
  },
  {
    path: 'form-edit',
    component: HomeComponent,
    // canActivate: [AuthGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }
