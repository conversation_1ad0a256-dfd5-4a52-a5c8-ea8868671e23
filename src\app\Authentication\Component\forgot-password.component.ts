import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../Services/auth.service';
import { first } from 'rxjs/operators';
import { ResetPasswordRequest } from '../Model/auth.model';

@Component({
  selector: 'app-forgot-password',
  templateUrl: '../Template/forgot-password.component.html',
  styleUrls: ['../Style/forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm!: FormGroup;
  loading = false;
  submitted = false;
  error = '';
  successMessage = '';

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private authService: AuthService
  ) {
    // Redirect to home if already logged in
    if (this.authService.isLoggedIn) {
      this.router.navigate(['/']);
    }
  }

  ngOnInit(): void {
    // Initialize forgot password form
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  // Convenience getter for easy access to form fields
  get f() { return this.forgotPasswordForm.controls; }

  onSubmit(): void {
    this.submitted = true;
    this.error = '';
    this.successMessage = '';

    // Stop here if form is invalid
    if (this.forgotPasswordForm.invalid) {
      return;
    }

    this.loading = true;
    
    const resetRequest: ResetPasswordRequest = {
      email: this.f['email'].value
    };

    this.authService.requestPasswordReset(resetRequest)
      .pipe(first())
      .subscribe({
        next: (response) => {
          this.successMessage = 'Password reset instructions have been sent to your email.';
          this.loading = false;
        },
        error: error => {
          this.error = error.message || 'Failed to request password reset';
          this.loading = false;
        }
      });
  }
}
