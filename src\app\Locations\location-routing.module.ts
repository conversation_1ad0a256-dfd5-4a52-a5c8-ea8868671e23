import { NgModule, Component } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LocationDataComponent } from './Component/location-data.component';
import { LocationViewComponent } from './Component/location-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    component: LocationDataComponent
  },
  {
    path: 'View/:id',
    component: LocationViewComponent
  },
  {
    path: 'View',
    component: LocationViewComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LocationRoutingModule { }
