import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DropdownData, FormListData } from '../Model/model';

@Component({
  selector: 'app-grouped-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template:`
  <div class="form-group">
  <i class="far fa-check-circle action-icons"></i>
  <button type="button" class="select-one-btn" (click)="openPopup()">
    {{  placeholder }}
  </button>

  <div *ngIf="getSelectedLabels().length" class="selected-chips">
    <span class="chip" *ngFor="let value of getSelectedLabels()">{{ value }}</span>
  </div>

  <div *ngIf="formGroup.get(fieldName)?.invalid && formGroup.get(fieldName)?.touched" class="error-message">
    This field is required
  </div>

  <div class="popup-backdrop" *ngIf="showPopup">
    <div class="popup-container">
      <div class="popup-header">
        <h3>{{Label}}</h3>
        <button type="button" class="close-btn" (click)="closePopup()">×</button>
      </div>

      <div class="search-container">
        <input
          type="text"
          class="search-input"
          placeholder="Search..."
          [(ngModel)]="searchTerm"
          (input)="onSearch()"
        />
        <button *ngIf="searchTerm" class="clear-search" (click)="clearSearch()">
          ×
        </button>
      </div>

      <div class="popup-content">
        <div class="popup-options">
          <div *ngIf="filteredGroups.length === 0" class="no-results">
            No results found for "{{ searchTerm }}"
          </div>
          <div *ngFor="let groupItem of filteredGroups">
            <strong *ngIf="!!groupItem.title">{{ groupItem.title }}</strong>
            <ul>
              <li *ngFor="let item of groupItem.items" class="option-item">
                <div class="option-container">
                  <div class="checkbox-container" *ngIf="isMultiSelect">
                    <input
                      type="checkbox"
                      [value]="item.value"
                      [checked]="isChecked(item.value)"
                      (change)="toggleOption(item.value)" />
                  </div>
                  <div class="option-text" (click)="toggleOption(item.value)">
                    {{ item.value }}
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div class="popup-footer" *ngIf="isMultiSelect">
          <button type="button" (click)="applyMultiSelect()">Apply</button>
        </div>
      </div>
    </div>
  </div>
</div>

  `,
  styles:`
  .form-group {
  margin-bottom: 1rem;
  position: relative;
}

.select-one-btn {
  padding: 8px 16px;
    font-weight: bold;
    background-color: white;
    border: 1px solid #007bff;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    text-transform: uppercase;

  &:hover {
    border-color: #888;
  }
}

.selected-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;

  .chip {
    background-color: #e0f3ff;
    border: 1px solid #91d5ff;
    padding: 4px 10px;
    border-radius: 14px;
    font-size: 13px;
    color: #007acc;
  }
}

.error-message {
  color: #ff4d4f;
  font-size: 13px;
  margin-top: 4px;
}

.popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  overflow: hidden;

  @media (max-width: 480px) {
    width: 95%;
    max-width: 100%;
    margin: 0 10px;
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;

  h3 {
    font-size: 16px;
    margin: 0;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #777;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
      color: #333;
    }

    @media (max-width: 480px) {
      font-size: 20px;
    }
  }

  @media (max-width: 480px) {
    padding: 12px 15px;
  }
}
.action-icons {
  color: #2196F3;
  font-size: 16px;
  text-decoration: none;
  transition: color 0.2s;
  padding: 5px;
  border: none;
}

.search-container {
  position: relative;
  padding: 10px 20px;
  border-bottom: 1px solid #eee;

  .search-input {
    width: 100%;
    padding: 10px 35px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    background-color: #f9f9f9;
    transition: all 0.2s;

    &:focus {
      outline: none;
      border-color: #007acc;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
    }
  }

  .clear-search {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;

    &:hover {
      background-color: #f0f0f0;
      color: #555;
    }
  }

  @media (max-width: 480px) {
    padding: 10px 15px;

    .search-input {
      padding: 10px 35px 10px 12px;
      font-size: 15px;
    }

    .clear-search {
      right: 25px;
      font-size: 18px;
    }
  }
}

.popup-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  max-height: calc(80vh - 120px); /* Adjust for header and search */
  background-color: #fff;
}

.popup-options {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  -webkit-overflow-scrolling: touch;
  max-height: 50vh;
  scrollbar-width: thin;
  scrollbar-color: #ccc #f5f5f5;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 6px;
  }

  @media (max-width: 480px) {
    max-height: 60vh;
  }

  .no-results {
    padding: 20px 0;
    text-align: center;
    color: #666;
    font-style: italic;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0 0 10px 0;

    .option-item {
      padding: 8px 20px;
      transition: background 0.2s;
      margin-bottom: 1px;
      position: relative;

      &:hover {
        background-color: #f8f8f8;
      }

      .option-container {
        display: flex;
        align-items: center;
      }

      .checkbox-container {
        flex: 0 0 auto;
        width: 20px;
        margin-right: 10px;

        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          border: 1px solid #ccc;
          border-radius: 3px;
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          position: relative;
          cursor: pointer;
          background-color: white;
          margin: 0;
          padding: 0;

          &:checked {
            background-color: #007acc;
            border-color: #007acc;
          }

          &:checked:after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 4px;
            height: 9px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }
      }

      .option-text {
        flex: 1;
        font-size: 14px;
        color: #333;
        cursor: pointer;
        padding-left: 5px;
      }

      @media (max-width: 480px) {
        padding: 10px 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 0;

        .option-text {
          font-size: 15px;
        }

        .checkbox-container input[type="checkbox"] {
          width: 20px;
          height: 20px;

          &:checked:after {
            left: 7px;
            top: 3px;
            width: 5px;
            height: 10px;
          }
        }
      }
    }
  }

  strong {
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    padding: 5px 20px;
    position: sticky;
    top: -12px;
    background-color: #f5f5f5;
    z-index: 1;
  }

  .single-select-item {
    display: block;
    width: 100%;
    padding: 10px 20px;
    cursor: pointer;
    transition: background 0.2s;
    color: #333;
    font-size: 14px;

    &:hover {
      background-color: #f8f8f8;
    }

    @media (max-width: 480px) {
      padding: 12px 15px;
      font-size: 15px;
      border-bottom: 1px solid #f0f0f0;
    }
  }

}

.popup-footer {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 10px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  @media (max-width: 480px) {
    padding: 10px 15px;
  }

  button {
    background-color: #0078d4;
    color: white;
    border: none;
    padding: 8px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    min-width: 100px;
    transition: background-color 0.2s, transform 0.1s;

    &:hover {
      background-color: #0066b8;
    }

    &:active {
      transform: translateY(1px);
    }

    @media (max-width: 480px) {
      padding: 10px 20px;
      font-size: 15px;
      width: 100%;
    }
  }
}

  `,
})
export class GroupedDropdownComponent implements OnInit {
  @Input() Label!: string;
  @Input() formGroup!: FormGroup;
  @Input() fieldName!: string;
  @Input() placeholder: string = 'Select';
  @Input() group!: DropdownData;
  @Input() isMultiSelect: boolean = true;

  showPopup = false;
  selectedOptions: string[] = [];
  searchTerm: string = '';
  filteredGroups: any[] = [];

  ngOnInit() {
    this.initializeSelectedOptions();
    this.filteredGroups = [...this.group.list];
    this.formGroup.get(this.fieldName)?.valueChanges.subscribe(value => {
      this.updateSelectedOptions(value);
    });
  }

  initializeSelectedOptions() {
    const currentVal = this.formGroup.get(this.fieldName)?.value;
    this.updateSelectedOptions(currentVal);
  }

  updateSelectedOptions(value: any) {
    if (this.isMultiSelect && Array.isArray(value)) {
      this.selectedOptions = [...value];
    } else if (value) {
      this.selectedOptions = [value];
    } else {
      this.selectedOptions = [];
    }
  }

  openPopup() {
    this.showPopup = true;
    this.filteredGroups = [...this.group.list];
    this.searchTerm = '';
  }

  closePopup() {
    this.selectedOptions = [];
    this.formGroup.get(this.fieldName)?.markAsTouched();
    this.showPopup = false;
  }

  isChecked(value: string): boolean {
    return this.selectedOptions.includes(value);
  }

  toggleOption(value: string) {
    if (this.isMultiSelect) {
      if (this.selectedOptions.includes(value)) {
        this.selectedOptions = this.selectedOptions.filter(v => v !== value);
      } else {
        this.selectedOptions.push(value);
      }
    } else {
      this.selectedOptions = [value];
      this.formGroup.get(this.fieldName)?.setValue(value);
      this.showPopup = false;

    }
  }

  applyMultiSelect() {
    this.formGroup.get(this.fieldName)?.setValue(this.selectedOptions);
    this.formGroup.get(this.fieldName)?.markAsTouched();
    this.showPopup = false;
  }

  getSelectedLabels(): string[] {
    const allItems = this.group.list.flatMap(g => g.items);
    return allItems
      .filter(item => this.selectedOptions.includes(item.value))
      .map(item => item.value);
  }

  onSearch() {
    if (!this.searchTerm || !this.searchTerm.trim()) {
      this.filteredGroups = [...this.group.list];
      return;
    }

    const term = this.searchTerm.toLowerCase().trim();

    this.filteredGroups = this.group.list.map(group => {
      const filteredItems = group.items.filter((item: any) =>
        (item.value && item.value.toLowerCase().includes(term))
      );

      if (filteredItems.length > 0) {
        return {
          ...group,
          items: filteredItems
        };
      }
      return null;
    }).filter(group => group !== null);
  }

  clearSearch() {
    this.searchTerm = '';
    this.filteredGroups = [...this.group.list];
  }
}
