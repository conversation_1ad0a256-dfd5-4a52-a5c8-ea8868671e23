/* Container for the entire forms page */
.forms-container {
  display: flex;
  height: calc(100vh - 60px); /* Adjust based on your header height */
  position: relative;
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
  transition: all 0.3s ease;
  overflow-y: auto;
  height: 100%;
}

.sidebar.collapsed {
  width: 0;
  overflow: hidden;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #ddd;
  background: linear-gradient(212deg, #667eea 0%, #137fd4 100%);
  color: white;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.sidebar-content {
  padding: 5px 0;
}

.forms-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-item {
  margin-bottom: 2px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.form-header:hover {
  background-color: #e0e0e0;
}

.form-header.active {
  background-color: #e8eaf6;
  border-left: 4px solid #3f51b5;
  padding-left: 11px; /* 15px - 4px border */
}

.form-name {
  display: block;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* Form Types List */
.form-types-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.form-type-item:hover {
  background-color: #e0e0e0;
}

.form-type-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Form Submissions List */
.submissions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.submission-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.2s;
}

.submission-item:hover {
  background-color: #e0e0e0;
}

.submission-item.active {
  background-color: #e8eaf6;
  border-left: 4px solid #3f51b5;
  padding-left: 11px;
}

.submission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.submission-id {
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
}

.submission-date {
  font-size: 0.7rem;
  color: #666;
}

.submission-info {
  font-size: 0.75rem;
  color: #555;
}

.submission-by {
  display: block;
}

/* Back button in header */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  color: #e0e0e0;
}

.no-forms {
  padding: 15px;
  color: #757575;
  font-style: italic;
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 5px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.main-content.expanded {
  flex: 1;
  padding: 10px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #757575;
  font-style: italic;
}

/* Sidebar toggle button */
.sidebar-toggle {
  position: absolute;
  left: 250px; /* Same as sidebar width */
  top: 10px;
  z-index: 100;
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
  transition: left 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: #303f9f;
}

.sidebar-toggle i {
  font-size: 14px;
}

/* When sidebar is collapsed, move the toggle button */
.sidebar.collapsed ~ .main-content .sidebar-toggle {
  left: 0;
}
.action-button {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
}

.action-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

/* Loading indicator styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 200px;
  height: 200px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3f51b5;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1.5s linear infinite;
  margin-bottom: 15px;
}

.loader-container p {
  font-size: 16px;
  color: #333;
  margin: 0;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Styles */
.dashboard-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}
/* Dashboard Cards */
.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.card-icon {
  background-color: #e8eaf6;
  color: #3f51b5;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 24px;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin: 0 0 5px 0;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0;
}


/* Dashboard Sidebar Styles */
.dashboard-forms-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dashboard-form-item {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dashboard-form-item:hover {
  background-color: #f5f5f5;
}

.dashboard-form-item.active {
  background-color: #e3f2fd;
  border-left: 3px solid #2196F3;
  padding-left: 12px;
}

.dashboard-form-header {
  display: flex;
  align-items: center;
}

.dashboard-form-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dashboard Chart */
.dashboard-chart-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  margin-top: 20px;
}

/* Chart Header with Date Controls */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.dashboard-section-title {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.date-range-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.date-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-control label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.date-input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

/* Chart Content Layout */
.chart-content-container {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.chart-wrapper {
  flex: 1;
  position: relative;
  height: 370px;
  min-width: 0; /* Prevents flex item from overflowing */
  width: 50%;
  margin-top: 0px
}

.chart-details {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-stats-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stats-label {
  color: #666;
  font-size: 14px;
}

.stats-value {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.daily-stats-list {
  margin-top: 10px;
}

.daily-stats-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #eee;
}

.stats-date {
  font-size: 13px;
  color: #666;
}

.stats-count {
  font-size: 13px;
  font-weight: 500;
  color: #3f51b5;
}

.stats-footer {
  margin-top: 10px;
  text-align: center;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.no-data-message {
  text-align: center;
  padding: 30px;
  color: #888;
  font-style: italic;
}

.no-data-message i {
  margin-right: 8px;
  color: #3f51b5;
}

.date-header {
  background-color: #3f51b5;
  color: white;
  font-weight: 600;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  border-radius: 4px 4px 0 0;
}

.date-text {
  font-size: 14px;
}

.date-count {
  /* background-color: white; */
  color: white;
}

.submission-row {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-bottom: 1px solid #e0e0e0;
}

.submission-row span {
  flex: 1; /* makes all span elements equal width */
  min-width: 100px;
}
.time {
  flex: 1;
}
.title {
  flex: 2;
}
.location {
  flex: 2;
}
.link-icon {
  flex: 0.5;
  text-align: center;
}
.flag-item {
  flex: 0.5;
  text-align: center;
  color: red;
}
.flag-item.hidden {
  visibility: hidden; /* takes space but hides icon */
}

/* Responsive Dashboard */
@media (max-width: 768px) {
  .dashboard-cards {
    flex-direction: column;
  }

  .dashboard-card {
    width: 100%;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-range-controls {
    margin-top: 10px;
    width: 100%;
  }

  .chart-wrapper {
    height: 300px;
  }

  .chart-content-container {
    flex-direction: column;
  }

  .chart-details {
    width: 100%;
    margin-top: 20px;
  }
}

@media (max-width: 576px) {
  .chart-wrapper {
    height: 250px;
  }

  .stats-item {
    flex-direction: column;
    margin-bottom: 12px;
  }

  .stats-value {
    margin-top: 4px;
  }
}
