export interface workers{
  id?:string;
  details:workersData;
  invitationToken?:string;
  isActive?:boolean;
  passwordHash?:string;
  roles?:string[];
  auditHistory?: {
    locationIds?: string[];
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
  };
}
export interface workersData{
  name:string;
  jobtitle:string;
  email:string;
  phoneno1:string;
  phoneno2:string;
  address:string;
  city:string;
  zipCode?:number;
  employeeNumber?:string;
  hiredDate?:Date;
  eContact1?:number;
  eContact2?:number;
  notes?:string;
}
