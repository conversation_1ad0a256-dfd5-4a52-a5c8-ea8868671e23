import { Data } from './../../../../node_modules/@progress/pako-esm/index.d';
import { Location, locationData } from '../Models/location';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { DataStateChangeEvent, GridDataResult } from '@progress/kendo-angular-grid';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { orderBy, process, SortDescriptor, State } from '@progress/kendo-data-query';
import { CoreDataService } from '../../core-data.service';

@Component({
  selector: 'app-location-data',
  templateUrl: './../Template/location-data.component.html',
  styleUrl: './../Styles/location-data.component.css'
})
export class LocationDataComponent {
constructor(private router:Router , private coreData: CoreDataService){this.allData = this.allData.bind(this);}
gridHeight: number = 0;
screenSize: number = 0;
public location: locationData[] = [];
public Filteredlist: locationData[] = [];
public filteredLocationsList: locationData[] = [];
public state: State = {
  skip: 0,
  take: 40,
  sort: [],
};

public gridData: GridDataResult = process(this.location, this.state);


ngOnInit(): void {
  this.screenSize = window.innerHeight;
  this.gridHeight = this.screenSize - 60;
  this.Locations();
}

public dataStateChange(state: DataStateChangeEvent): void {
  this.state = state;
  this.gridData = process(this.Filteredlist, this.state);
}
public sortChange(sort: SortDescriptor[]): void {
  this.gridData = process(orderBy(this.Filteredlist, sort), this.state);
}
onCellClick(e:any){
  let rowIndex;
  rowIndex = e.target.parentElement.rowIndex;
  let rowData = this.gridData.data[rowIndex];
  this.router.navigate(['Locations/View',rowData.id]);
}

Locations(){

 this.coreData.GetLocationForGrid().subscribe({
  next: (locationGet: Location[]) => {
     const locationList = locationGet.map(item => {
        return {
          id: item.id,
          ...item.details,
        };
      });
      this.location = locationList;
      this.Filteredlist = locationList;

      console.log(this.location , 'GridDATA');
      // Process the grid data using the current state
      // this.gridData = process(this.Filteredlist, this.state);
      this.gridData = {
      data: this.location.slice(this.state.skip, this.state.take),
      total: this.location.length,
    };
  },
  error: (err) => {
    console.error('Error fetching location data:', err);
  }
});
}


  public allData(): ExcelExportData {
    const result: ExcelExportData = {
      data: process(this.location, {
        filter: this.state.filter,
        sort: this.state.sort,
      }).data,
    };
    return result;
  }

onLocation(){
  this.router.navigate(['Locations/View']);
}
}
