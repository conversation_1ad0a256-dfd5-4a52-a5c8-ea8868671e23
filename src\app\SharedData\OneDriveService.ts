import { Injectable } from '@angular/core';
import { MsalService } from '@azure/msal-angular';
import { AccountInfo, InteractionRequiredAuthError } from '@azure/msal-browser';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, from, throwError, firstValueFrom, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { MsalAuthService } from './MsalAuthService';

// Declare OneDrive as a global variable
declare global {
  interface Window {
    OneDrive: any;
  }
}

export interface OneDriveFolder {
  id: string;
  name: string;
  driveId?: string;
  parentReference?: any;
  webUrl?: string;
  children?: OneDriveFolder[];
  isExpanded?: boolean;
  isLoading?: boolean;
  level?: number;
}

export interface OneDriveUploadProgress {
  isLoading: boolean;
  progress: number;
  error: string;
}

@Injectable({ providedIn: 'root' })
export class OneDriveService {
  private readonly scopes = ['User.Read', 'Files.ReadWrite.All'];
  account: AccountInfo | null = null;
  private scriptLoaded = false;
  OneDrive: any;

  // Observable sources
  private foldersSubject = new BehaviorSubject<OneDriveFolder[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string>('');
  private uploadProgressSubject = new BehaviorSubject<OneDriveUploadProgress>({
    isLoading: false,
    progress: 0,
    error: ''
  });
  private currentFolderSubject = new BehaviorSubject<OneDriveFolder | null>(null);

  // Observable streams
  folders$ = this.foldersSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();
  error$ = this.errorSubject.asObservable();
  uploadProgress$ = this.uploadProgressSubject.asObservable();
  currentFolder$ = this.currentFolderSubject.asObservable();

  constructor(private msalService: MsalService, private http: HttpClient,private msalAuth: MsalAuthService) {

    this.initAccount();
  }

  ngOnInit(): void {
    // this.onLoginClick();
    this.msalService.instance.handleRedirectPromise().then(result => {
      if (result?.account) {
        this.msalService.instance.setActiveAccount(result.account);
      } else {
        const accounts = this.msalService.instance.getAllAccounts();
        if (accounts.length > 0) {
          this.msalService.instance.setActiveAccount(accounts[0]);
        }
      }
    });
  }



  private initAccount() {
    const accounts = this.msalService.instance.getAllAccounts();
    if (accounts.length > 0) {
      this.account = accounts[0];
      this.msalService.instance.setActiveAccount(this.account);
    }
  }

  /**
   * Get the active account or throw an error if not authenticated
   */
  private getActiveAccount(): AccountInfo {
    const activeAccount = this.msalService.instance.getActiveAccount();
    if (!activeAccount) {
      throw new Error('No active account. User must sign in first.');
    }
    return activeAccount;
  }

  /**
   * Acquire an access token silently
   * @param forceAccountSelection Whether to force account selection even if user is already logged in
   */
  private async acquireToken(forceAccountSelection: boolean = false): Promise<string> {
    try {
      // If we're forcing account selection, skip silent token acquisition
      if (forceAccountSelection) {
        throw new InteractionRequiredAuthError('Forcing account selection');
      }

      // Check if there are multiple accounts
      const accounts = this.msalService.instance.getAllAccounts();

      // If multiple accounts exist and no active account is set, force account selection
      if (accounts.length > 1 && !this.msalService.instance.getActiveAccount()) {
        throw new InteractionRequiredAuthError('Multiple accounts detected, selection required');
      }

      const activeAccount = this.getActiveAccount();
      const result = await this.msalService.instance.acquireTokenSilent({
        account: activeAccount,
        scopes: this.scopes
      });
      return result.accessToken;
    } catch (error) {
      if (error instanceof InteractionRequiredAuthError) {
        console.log('Interaction required, using popup for login...');
        return new Promise((resolve, reject) => {
          // Determine if we need to force account selection
          const shouldForceSelection = forceAccountSelection ||
                                      this.msalService.instance.getAllAccounts().length > 1;

          const loginRequest = shouldForceSelection ?
            { scopes: this.scopes, prompt: 'select_account' } :
            { scopes: this.scopes };

          this.msalService.loginPopup(loginRequest).subscribe({
            next: (response: any) => {
              this.msalService.instance.setActiveAccount(response.account);
              this.msalService.instance.acquireTokenSilent({
                account: response.account,
                scopes: this.scopes
              }).then(result => {
                resolve(result.accessToken);
              }).catch(err => {
                reject(err);
              });
            },
            error: (err: any) => {
              console.error('Login popup failed:', err);
              reject(err);
            }
          });
        });
      }
      return Promise.reject(error);
    }
  }

  /**
   * Load OneDrive folders
   * @returns Observable of folders
   */
  loadOneDriveFolders(): Observable<OneDriveFolder[]> {
    this.loadingSubject.next(true);
    this.errorSubject.next('');

    const activeAccount = this.msalService.instance.getActiveAccount();

    if (!activeAccount) {
      this.loadingSubject.next(false);
      this.errorSubject.next('Not authenticated. Logging in...');

      // Use popup login instead of redirect
      return from(new Promise<OneDriveFolder[]>((resolve, reject) => {
        this.msalService.loginPopup({
          scopes: this.scopes
        }).subscribe({
          next: (response: any) => {
            this.msalService.instance.setActiveAccount(response.account);
            // Retry loading folders after successful login
            this.loadOneDriveFolders().subscribe({
              next: folders => resolve(folders),
              error: err => reject(err)
            });
          },
          error: (error: any) => {
            this.loadingSubject.next(false);
            const errorMsg = 'Popup login failed: ' + (error.message || 'Unknown error');
            this.errorSubject.next(errorMsg);
            reject(new Error(errorMsg));
          }
        });
      }));
    }

    // Check if there are multiple accounts that might need selection
    const accounts = this.msalService.instance.getAllAccounts();
    const needsAccountSelection = accounts.length >= 1;

    // Use our acquireToken method which handles account selection
    return from(this.acquireToken(needsAccountSelection)).pipe(
      switchMap(accessToken => {
        const headers = new HttpHeaders({
          'Authorization': `Bearer ${accessToken}`
        });

        // Get folders from OneDrive root
        return this.http.get<any>('https://graph.microsoft.com/v1.0/me/drive/root/children?$filter=folder ne null', { headers }).pipe(
          map(response => {
            const folders = response.value.map((folder: any) => ({
              id: folder.id,
              name: folder.name,
              driveId: folder.parentReference?.driveId,
              parentReference: folder.parentReference,
              webUrl: folder.webUrl,
              children: [],
              isExpanded: false,
              isLoading: false,
              level: 0 // Root level folders
            }));

            // Always update the folders subject and loading state
            this.foldersSubject.next(folders);
            this.loadingSubject.next(false);

            if (folders.length === 0) {
              console.log('No folders found in OneDrive');
            } else {
              console.log(`Found ${folders.length} folders in OneDrive`);
            }

            return folders;
          }),
          catchError(error => {
            console.error('Error fetching folders:', error);
            this.loadingSubject.next(false);
            this.errorSubject.next('Failed to fetch folders: ' + (error.message || error.statusText || 'Unknown error'));

            // Clear the folders to indicate an error state
            this.foldersSubject.next([]);

            return throwError(() => error);
          })
        );
      }),
      catchError(error => {
        console.error('Token acquisition failed:', error);
        this.loadingSubject.next(false);
        this.errorSubject.next('Authentication error: ' + (error.message || 'Unknown error'));

        // Clear the folders to indicate an error state
        this.foldersSubject.next([]);

        if (error instanceof InteractionRequiredAuthError) {
          // Handle with popup login instead of redirect
          return from(new Promise<OneDriveFolder[]>((resolve, reject) => {
            this.msalService.loginPopup({
              scopes: this.scopes
            }).subscribe({
              next: (response: any) => {
                this.msalService.instance.setActiveAccount(response.account);
                // Retry loading folders after successful login
                this.loadOneDriveFolders().subscribe({
                  next: folders => resolve(folders),
                  error: err => reject(err)
                });
              },
              error: (err: any) => {
                this.loadingSubject.next(false);
                const errorMsg = 'Popup login failed: ' + (err.message || 'Unknown error');
                this.errorSubject.next(errorMsg);
                reject(new Error(errorMsg));
              }
            });
          }));
        }

        return throwError(() => error);
      })
    );
  }

  /**
   * Upload a file to a selected OneDrive folder
   * @param file File to upload
   * @param folderId ID of the folder to upload to
   * @returns Promise with the web URL of the uploaded file
   */
  async uploadToSelectedFolder(file: File, folderId: string): Promise<string> {
    if (!file) {
      this.uploadProgressSubject.next({
        isLoading: false,
        progress: 0,
        error: 'No file to upload'
      });
      return Promise.reject(new Error('No file to upload'));
    }

    if (!folderId) {
      this.uploadProgressSubject.next({
        isLoading: false,
        progress: 0,
        error: 'Please select a folder first'
      });
      return Promise.reject(new Error('Please select a folder first'));
    }

    this.uploadProgressSubject.next({
      isLoading: true,
      progress: 10,
      error: ''
    });

    const activeAccount = this.msalService.instance.getActiveAccount();

    if (!activeAccount) {
      this.uploadProgressSubject.next({
        isLoading: false,
        progress: 0,
        error: 'Not authenticated. Logging in...'
      });

      // Use popup login instead of redirect
      return new Promise((resolve, reject) => {
        this.msalService.loginPopup({
          scopes: this.scopes
        }).subscribe({
          next: (response: any) => {
            this.msalService.instance.setActiveAccount(response.account);
            // Retry upload after successful login
            this.uploadToSelectedFolder(file, folderId)
              .then(webUrl => resolve(webUrl))
              .catch(err => reject(err));
          },
          error: (error: any) => {
            this.uploadProgressSubject.next({
              isLoading: false,
              progress: 0,
              error: 'Popup login failed: ' + (error.message || 'Unknown error')
            });
            reject(error);
          }
        });
      });
    }

    try {
      // Check if there are multiple accounts that might need selection
      const accounts = this.msalService.instance.getAllAccounts();
      const needsAccountSelection = accounts.length > 1;

      // Use our acquireToken method which handles account selection
      const accessToken = await this.acquireToken(needsAccountSelection);

      const headers = new HttpHeaders({
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': file.type
      });

      this.uploadProgressSubject.next({
        isLoading: true,
        progress: 30,
        error: ''
      });

      const uploadUrl = `https://graph.microsoft.com/v1.0/me/drive/items/${folderId}:/${file.name}:/content`;

      this.uploadProgressSubject.next({
        isLoading: true,
        progress: 50,
        error: ''
      });

      const response = await firstValueFrom(
        this.http.put<any>(uploadUrl, file, { headers })
      );

      this.uploadProgressSubject.next({
        isLoading: true,
        progress: 100,
        error: ''
      });

      console.log('Upload success', response);

      // Reset the upload progress after a delay
      setTimeout(() => {
        this.uploadProgressSubject.next({
          isLoading: false,
          progress: 0,
          error: ''
        });
      }, 1000);

      return response.webUrl;
    } catch (error: any) {
      if (error instanceof InteractionRequiredAuthError) {
        // Handle with popup login
        return new Promise((resolve, reject) => {
          this.msalService.loginPopup({
            scopes: this.scopes
          }).subscribe({
            next: (response: any) => {
              this.msalService.instance.setActiveAccount(response.account);
              // Retry upload after successful login
              this.uploadToSelectedFolder(file, folderId)
                .then(webUrl => resolve(webUrl))
                .catch(err => reject(err));
            },
            error: (err: any) => {
              this.uploadProgressSubject.next({
                isLoading: false,
                progress: 0,
                error: 'Popup login failed: ' + (err.message || 'Unknown error')
              });
              reject(err);
            }
          });
        });
      }

      this.uploadProgressSubject.next({
        isLoading: false,
        progress: 0,
        error: ''
      });

      return Promise.reject(new Error());
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async uploadFileToOneDrive(file: File, folderId: string): Promise<string> {
    try {
      return await this.uploadToSelectedFolder(file, folderId);
    } catch (error: any) {
      console.error('Error in uploadFileToOneDrive:', error);
      throw error;
    }
  }

  /**
   * Load the OneDrive SDK
   */
  loadOneDriveSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if the SDK is already loaded
      if (this.scriptLoaded && window.OneDrive) {
        console.log('OneDrive SDK already loaded, using existing instance');
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.live.net/v7.2/OneDrive.js';
      script.type = 'text/javascript';
      script.async = true;

      // On successful load, set the scriptLoaded flag to true
      script.onload = () => {
        this.scriptLoaded = true;
        console.log('OneDrive SDK loaded successfully');

        // Add a small delay to ensure the SDK is fully initialized
        setTimeout(() => {
          if (window.OneDrive) {
            console.log('OneDrive global object is available');
            // Store a reference to the OneDrive object
            this.OneDrive = window.OneDrive;
            resolve();
          } else {
            console.error('OneDrive global object not available after script load');
            reject(new Error('OneDrive global object not available after script load'));
          }
        }, 500);
      };

      // Handle errors in script loading
      script.onerror = (error) => {
        console.error('Error loading OneDrive SDK', error);
        reject(error);
      };

      // Append the script tag to the head of the document
      document.head.appendChild(script);
    });
  }

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.msalService.instance.getActiveAccount();
  }

  /**
   * Load subfolders of a specific folder
   * @param folderId ID of the folder to load subfolders from
   * @returns Observable of folders
   */
  loadSubfolders(folderId: string): Observable<OneDriveFolder[]> {
    if (!folderId) {
      return throwError(() => new Error('Folder ID is required'));
    }

    this.loadingSubject.next(true);
    this.errorSubject.next('');

    return from(this.acquireToken()).pipe(
      switchMap(accessToken => {
        const headers = new HttpHeaders({
          'Authorization': `Bearer ${accessToken}`
        });

        // Get subfolders from the specified folder
        return this.http.get<any>(`https://graph.microsoft.com/v1.0/me/drive/items/${folderId}/children?$filter=folder ne null`, { headers }).pipe(
          map(response => {
            const subfolders = response.value.map((folder: any) => ({
              id: folder.id,
              name: folder.name,
              driveId: folder.parentReference?.driveId,
              parentReference: folder.parentReference,
              webUrl: folder.webUrl,
              children: [],
              isExpanded: false,
              isLoading: false,
              level: 1 // Default level for subfolders
            }));

            this.loadingSubject.next(false);

            if (subfolders.length === 0) {
              console.log(`No subfolders found in folder ${folderId}`);
            } else {
              console.log(`Found ${subfolders.length} subfolders in folder ${folderId}`);
            }

            return subfolders;
          }),
          catchError(error => {
            console.error('Error fetching subfolders:', error);
            this.loadingSubject.next(false);
            this.errorSubject.next('Failed to fetch subfolders: ' + (error.message || error.statusText || 'Unknown error'));
            return throwError(() => error);
          })
        );
      }),
      catchError(error => {
        console.error('Token acquisition failed:', error);
        this.loadingSubject.next(false);
        this.errorSubject.next('Authentication error: ' + (error.message || 'Unknown error'));
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new folder inside a parent folder
   * @param parentFolderId ID of the parent folder (use 'root' for root folder)
   * @param folderName Name of the new folder
   * @returns Promise with the created folder
   */
  async createFolder(parentFolderId: string, folderName: string): Promise<OneDriveFolder> {
    if (!folderName) {
      return Promise.reject(new Error('Folder name is required'));
    }

    this.loadingSubject.next(true);
    this.errorSubject.next('');

    try {
      const accessToken = await this.acquireToken();

      const headers = new HttpHeaders({
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      });

      // Prepare the request body
      const requestBody = {
        name: folderName,
        folder: {},
        '@microsoft.graph.conflictBehavior': 'rename'
      };

      // API endpoint depends on whether we're creating in root or in a specific folder
      const endpoint = parentFolderId === 'Home'
        ? 'https://graph.microsoft.com/v1.0/me/drive/root/children'
        : `https://graph.microsoft.com/v1.0/me/drive/items/${parentFolderId}/children`;

      const response = await firstValueFrom(
        this.http.post<any>(endpoint, requestBody, { headers })
      );

      this.loadingSubject.next(false);

      console.log('Folder created successfully:', response);

      // Convert the response to our folder interface
      const newFolder: OneDriveFolder = {
        id: response.id,
        name: response.name,
        driveId: response.parentReference?.driveId,
        parentReference: response.parentReference,
        webUrl: response.webUrl,
        children: [],
        isExpanded: false,
        isLoading: false,
        level: 0
      };

      // If we created a folder in the root, refresh the root folders
      if (parentFolderId === 'Home') {
        this.loadOneDriveFolders().subscribe();
      }

      return newFolder;
    } catch (error: any) {
      this.loadingSubject.next(false);
      this.errorSubject.next('Failed to create folder: ' + (error.message || error.statusText || 'Unknown error'));
      console.error('Error creating folder:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Set the current active folder
   * @param folder The folder to set as current
   */
  setCurrentFolder(folder: OneDriveFolder | null): void {
    this.currentFolderSubject.next(folder);
  }

  /**
   * Login with popup
   * @param accountSelection Whether to force account selection even if user is already logged in
   */
  login(accountSelection: boolean = false): Observable<any> {
    // If account selection is requested, use the account selection experience
    if (accountSelection) {
      return this.msalService.loginPopup({
        scopes: this.scopes,
        prompt: 'select_account' // Force account selection UI
      });
    }

    // Check if there are multiple accounts
    const accounts = this.msalService.instance.getAllAccounts();

    // If multiple accounts exist, force account selection
    if (accounts.length > 1) {
      console.log('Multiple accounts detected, showing account selection');
      return this.msalService.loginPopup({
        scopes: this.scopes,
        prompt: 'select_account' // Force account selection UI
      });
    }

    // Normal login flow
    return this.msalService.loginPopup({
      scopes: this.scopes
    });
  }
}
