import { CoreDataService } from './../core-data.service';
import { Injectable } from '@angular/core';
import { PublicClientApplication, Configuration, IPublicClientApplication } from '@azure/msal-browser';
import { MsalService } from '@azure/msal-angular';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class MsalAuthService {
  clientId!: string;
  tenantId!: string;


  constructor(private coreData:CoreDataService) {
    this.loadConfig();
  }

  async loadConfig(): Promise<void> {
    const data = await this.coreData.GetAsureID().toPromise();
    // this.clientId = data.clientID;
    // this.tenantId = data.tenantID;
    localStorage.setItem('msal_asur_id', data.id);
    localStorage.setItem('msal_client_id', data.clientID);
    localStorage.setItem('msal_tenant_id', data.tenantID);
  }

   getMsalConfig(): Configuration {
    return {
      auth: {
        clientId: localStorage.getItem('msal_client_id')!,
        authority: `https://login.microsoftonline.com/${localStorage.getItem('msal_tenant_id')!}`,
        redirectUri: window.location.origin,
      }
    };
  }

}
