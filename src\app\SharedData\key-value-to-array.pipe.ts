import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'keyValueToArray',
  standalone: true
})
export class KeyValueToArrayPipe implements PipeTransform {
  transform(value: { [key: string]: string | ArrayBuffer | null }): { key: string, data: ArrayBuffer }[] {
    if (!value) {
      return [];
    }
    
    return Object.entries(value)
      .filter(([_, val]) => val !== null)
      .map(([key, val]) => {
        // Convert string or ArrayBuffer to ArrayBuffer
        let data: ArrayBuffer;
        if (typeof val === 'string') {
          // Convert base64 string to ArrayBuffer
          const binary = atob(val.split(',')[1] || val);
          const bytes = new Uint8Array(binary.length);
          for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
          }
          data = bytes.buffer;
        } else if (val instanceof ArrayBuffer) {
          data = val;
        } else {
          // Default empty buffer for null values (should not happen due to filter)
          data = new ArrayBuffer(0);
        }
        
        return { key, data };
      });
  }
}
