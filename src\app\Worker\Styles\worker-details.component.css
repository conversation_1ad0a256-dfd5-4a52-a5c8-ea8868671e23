.Workers-Data-View{
    display: flex;
  height: calc(100vh - 60px);
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON>ri';
}
/* Form Section Styles */
.form-section {
  margin-bottom: 2.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.section-icon {
  color: #3b82f6;
  font-size: 1.1rem;
  margin-right: 0.75rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

/* Form Grid Layout */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  align-items: start;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

/* Field Labels */
.field-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Input Styles */
.field-input,
.field-textarea {
  padding: 0.75rem;
  border:none;
  border-bottom: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #374151;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.field-input:focus,
.field-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.field-input::placeholder,
.field-textarea::placeholder {
  color: #9ca3af;
}

.field-textarea {
  min-height: 80px;
  resize: vertical;
}

/* Error States */
.field-input.error,
.field-textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-input.error:focus,
.field-textarea.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Error Messages */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.3rem;
  display: flex;
  align-items: center;
  animation: slideInError 0.2s ease-out;
}

.error-message::before {
  content: "⚠";
  margin-right: 0.3rem;
  font-size: 0.8rem;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Photo Upload Placeholder */
.photo-upload {
  grid-row: 1 / 4;
  grid-column: 2;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-top: 1.5rem;
}

.photo-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  cursor: pointer;
  transition: border-color 0.2s ease, color 0.2s ease;
}

.photo-placeholder:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.photo-placeholder i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.photo-placeholder span {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.save-button {
  padding: 0.7rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 6px;
  background-color: #3b82f6;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-family: inherit;
}

.save-button:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.save-button:active {
  transform: translateY(0);
}

.cancel-button {
  padding: 0.7rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 6px;
  background-color: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-family: inherit;
}

.cancel-button:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.cancel-button:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-field.full-width {
    grid-column: 1;
  }

  .photo-upload {
    grid-row: auto;
    grid-column: 1;
    justify-content: flex-start;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  .photo-placeholder {
    width: 100px;
    height: 100px;
  }

  .form-actions {
    flex-direction: column;
  }

  .save-button,
  .cancel-button {
    width: 100%;
  }
}

/* User Location Management Styles */
.user-location-management-section {
  padding: 0;
}

.user-location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.user-location-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-location-title i {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.user-location-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-location-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.user-add-location-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-add-location-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.user-add-location-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #6b7280;
  font-size: 14px;
}

.user-loading-state i {
  font-size: 18px;
  color: #667eea;
}

.user-locations-container {
  min-height: 200px;
}

.user-no-locations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.user-no-locations i {
  font-size: 35px;
  color: #d1d5db;
  margin-bottom: 5px;
}

.user-no-locations h4 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.user-no-locations p {
  font-size: 14px;
  margin: 0 0 24px 0;
  max-width: 300px;
}

.user-add-first-location-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-add-first-location-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.user-add-first-location-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-locations-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 4px;
  justify-content: flex-start;
}

/* When there are 3 or more cards, use grid for even distribution */
.user-locations-grid:has(.user-location-card:nth-child(3)) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 8px;
}

/* Alternative approach for browsers that don't support :has() */
@media (min-width: 1200px) {
  .user-locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px;
  }

  /* Override for 1-2 cards to use flex */
  .user-locations-grid:not(:has(.user-location-card:nth-child(3))) {
    display: flex;
    justify-content: flex-start;
  }
}

@media (min-width: 900px) and (max-width: 1199px) {
  .user-locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px;
  }

  /* Override for 1-2 cards to use flex */
  .user-locations-grid:not(:has(.user-location-card:nth-child(3))) {
    display: flex;
    justify-content: flex-start;
  }
}

.user-location-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  width: 20vw;
  padding: 20px;
  position: relative;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  max-width: 350px;
  cursor: pointer;
}

.user-location-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.user-location-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.user-location-avatar i {
  font-size: 20px;
  color: white;
}

.user-location-info {
  flex: 1;
}

.user-location-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.user-remove-location-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.user-remove-location-btn:hover {
  background: #fecaca;
  color: #b91c1c;
  transform: scale(1.1);
}

/* Modal Styles */
.user-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.user-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideInModal 0.3s ease;
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.user-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-modal-close {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.user-modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.user-modal-body {
  padding: 24px;
}

.user-select-location-section {
  margin-bottom: 20px;
}

.user-select-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.user-location-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background-color: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.user-location-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.user-no-available-locations {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
}

.user-no-available-locations i {
  color: #9ca3af;
  font-size: 16px;
}

.user-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.user-modal-cancel-btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-modal-cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.user-modal-add-btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-modal-add-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.user-modal-add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile Responsive for Location Management */
@media (max-width: 768px) {
  .user-location-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .user-locations-grid {
    display: flex !important;
    flex-direction: column;
    gap: 8px;
  }

  .user-location-card {
    padding: 16px;
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
  }

  .user-modal-content {
    width: 95%;
    margin: 20px;
  }

  .user-modal-header,
  .user-modal-body,
  .user-modal-footer {
    padding: 16px;
  }

  .user-modal-footer {
    flex-direction: column;
  }

  .user-modal-cancel-btn,
  .user-modal-add-btn {
    width: 100%;
    justify-content: center;
  }
}

