import { Injectable, ElementRef } from '@angular/core';
import SignaturePad from 'signature_pad';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignatureService {
  private signaturePad: SignaturePad | null = null;
  private signaturePadElement: ElementRef | null = null;
  private signatureColor: string = 'black';
  private signaturePadInitializing = false;
  private pendingSignatureData: string | null = null;

  // Observable to notify components when signature pad is initialized
  private signaturePadInitializedSubject = new BehaviorSubject<boolean>(false);
  public signaturePadInitialized$ = this.signaturePadInitializedSubject.asObservable();

  // Observable to notify components when signature data changes
  private signatureDataSubject = new BehaviorSubject<string | null>(null);
  public signatureData$ = this.signatureDataSubject.asObservable();

  constructor() { }

  /**
   * Set the signature pad element reference
   */
  setSignaturePadElement(element: ElementRef): void {
    this.signaturePadElement = element;
    // Initialize the signature pad when the element is set
    this.initializeSignaturePad();
  }

  /**
   * Set the signature color
   */
  setSignatureColor(color: string): void {
    this.signatureColor = color;
    if (this.signaturePad) {
      this.signaturePad.penColor = color;
    }
  }

  /**
   * Get the current signature color
   */
  getSignatureColor(): string {
    return this.signatureColor;
  }

  /**
   * Store signature data to be applied when pad is ready
   */
  setPendingSignatureData(data: string | null): void {
    this.pendingSignatureData = data;
    this.signatureDataSubject.next(data);

    // If signature pad is already initialized, apply the data immediately
    if (this.signaturePad) {
      this.applyStoredSignature();
    }
  }

  /**
   * Get the current signature data
   */
  getSignatureData(): string | null {
    if (this.signaturePad && !this.signaturePad.isEmpty()) {
      return this.signaturePad.toDataURL();
    }
    return this.pendingSignatureData;
  }

  /**
   * Initialize or reinitialize the signature pad
   */
  initializeSignaturePad(): void {
    // Prevent multiple simultaneous initialization attempts
    if (this.signaturePadInitializing) {
      return;
    }

    this.signaturePadInitializing = true;
    this.signaturePadInitializedSubject.next(false);
    // console.log('Starting signature pad initialization');

    // Use a timeout to ensure the DOM is fully rendered
    setTimeout(() => {
      if (this.signaturePadElement && this.signaturePadElement.nativeElement) {
        try {
          // Check if canvas is visible (not in a collapsed section)
          const canvas = this.signaturePadElement.nativeElement;

          // Force the canvas to be visible by ensuring its parent sections are expanded
          // This is a more aggressive approach to ensure the canvas is visible
          this.ensureCanvasIsVisible(canvas);

          // Check visibility again after ensuring it's visible
          const isVisible = canvas.offsetParent !== null &&
                           canvas.offsetWidth > 0 &&
                           canvas.offsetHeight > 0;

          if (!isVisible) {
            console.log('Signature pad canvas is still not visible after attempt to make it visible, will try again');

            // Try again after a longer delay
            setTimeout(() => {
              this.initializeSignaturePadInstance();
              this.signaturePadInitializing = false;
            }, 500);
            return;
          }

          // Canvas is visible, initialize the signature pad
          this.initializeSignaturePadInstance();
        } catch (error) {
          console.error('Error initializing signature pad:', error);
        }
      } else {
        // Try again after a longer delay
        setTimeout(() => {
          this.initializeSignaturePad();
        }, 500);
      }

      this.signaturePadInitializing = false;
    }, 300);
  }

  /**
   * Helper method to ensure the canvas is visible by expanding parent sections
   */
  private ensureCanvasIsVisible(canvas: HTMLCanvasElement): void {
    try {
      // Find all parent elements with class 'section-content' (collapsed sections)
      let parent = canvas.parentElement;
      while (parent) {
        // If this is a section content that might be collapsed
        if (parent.classList && parent.classList.contains('section-content')) {
          // Make sure it's displayed
          if (parent.style.display === 'none') {
            parent.style.display = 'block';
          }
        }

        // If this is a section with a collapse toggle
        if (parent.classList && parent.classList.contains('section')) {
          // Make sure it's not collapsed
          const isCollapsed = parent.classList.contains('collapsed');
          if (isCollapsed) {
            // console.log('Found collapsed section, removing collapsed class');
            parent.classList.remove('collapsed');
          }
        }

        parent = parent.parentElement;
      }

      // Ensure the canvas itself has proper dimensions
      if (canvas.width === 0 || canvas.height === 0) {
        canvas.width = 400;
        canvas.height = 150;
      }
    } catch (error) {
      console.error('Error ensuring canvas visibility:', error);
    }
  }

  /**
   * Helper method to initialize the signature pad instance
   */
  private initializeSignaturePadInstance(): void {
    // Clear any existing signature pad
    if (this.signaturePad) {
      try {
        this.signaturePad.clear();
        this.signaturePad.off(); // Remove event listeners
        this.signaturePad = null; // Completely remove the old instance
      } catch (e) {
        console.log('Error clearing existing signature pad:', e);
      }
    }

    try {
      if (!this.signaturePadElement || !this.signaturePadElement.nativeElement) {
        console.error('Signature pad element is not available');
        return;
      }

      const canvas = this.signaturePadElement.nativeElement;

      // Ensure the canvas has proper dimensions before initializing
      if (canvas.width === 0 || canvas.height === 0) {
        canvas.width = 400;
        canvas.height = 150;
      }

      // Reset the canvas to clear any previous content
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // Initialize the signature pad with proper settings
      this.signaturePad = new SignaturePad(canvas, {
        penColor: this.signatureColor,
        backgroundColor: 'white',
        minWidth: 1,
        maxWidth: 3,
        velocityFilterWeight: 0.7
      });
      // Notify subscribers that the signature pad is initialized
      this.signaturePadInitializedSubject.next(true);

      // Apply any pending signature data after successful initialization
      if (this.pendingSignatureData) {
        this.applyStoredSignature();
      }
    } catch (error) {
      console.error('Failed to initialize signature pad:', error);

      // Try again after a delay
      setTimeout(() => {
        this.initializeSignaturePad();
      }, 1000);
    }
  }

  /**
   * Apply stored signature data to the signature pad if available
   */
  private applyStoredSignature(): void {
    if (!this.pendingSignatureData || !this.signaturePad) {
      return;
    }

    try {
      // Create a new image to preload the signature data
      const img = new Image();
      img.onload = () => {
        try {
          // Clear the pad first
          this.signaturePad?.clear();

          // Get canvas dimensions
          const canvas = this.signaturePadElement?.nativeElement;
          if (!canvas) {
            console.error('Canvas element not available');
            return;
          }

          // Draw the image directly to the canvas first
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

            // Then apply to signature pad to ensure it recognizes the signature
            this.signaturePad?.fromDataURL(this.pendingSignatureData!);
          }
        } catch (e) {
          console.error('Error in image onload handler:', e);
          this.fallbackSignatureApplication();
        }
      };

      img.onerror = () => {
        console.error('Error loading signature image');
        this.fallbackSignatureApplication();
      };

      // Set the source to trigger loading
      img.src = this.pendingSignatureData;
    } catch (error) {
      console.error('Error applying stored signature:', error);
      this.fallbackSignatureApplication();
    }
  }

  /**
   * Fallback method to apply signature data with retries
   */
  private fallbackSignatureApplication(): void {

    // Try multiple times with increasing delays
    let retryCount = 0;
    const maxRetries = 3;

    const tryApplySignature = () => {
      if (retryCount >= maxRetries || !this.pendingSignatureData || !this.signaturePad) {
        return;
      }

      retryCount++;
      console.log(`Retry ${retryCount} of ${maxRetries}`);

      try {
        this.signaturePad.clear();
        this.signaturePad.fromDataURL(this.pendingSignatureData);
      } catch (e) {
        console.error(`Failed to apply signature on retry ${retryCount}:`, e);

        // Try again with a longer delay
        setTimeout(tryApplySignature, 500 * retryCount);
      }
    };

    // Start the retry process
    setTimeout(tryApplySignature, 500);
  }

  /**
   * Clear the signature pad
   */
  clearSignature(): void {
    if (this.signaturePad) {
      this.signaturePad.clear();
      this.pendingSignatureData = null;
      this.signatureDataSubject.next(null);
    }
  }

  /**
   * Save the current signature as a data URL
   */
  saveSignature(): string | null {
    if (this.signaturePad && !this.signaturePad.isEmpty()) {
      const signatureDataUrl = this.signaturePad.toDataURL();
      this.pendingSignatureData = signatureDataUrl;
      this.signatureDataSubject.next(signatureDataUrl);
      return signatureDataUrl;
    }
    return null;
  }

  /**
   * Check if the signature pad is empty
   */
  isSignatureEmpty(): boolean {
    return this.signaturePad ? this.signaturePad.isEmpty() : true;
  }
}
