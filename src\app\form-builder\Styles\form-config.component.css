.form-config {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #0973c9 100%);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
}
.sidebar-content {
  padding: 20px 0;
  position: relative;
  z-index: 2;
}
.sidebar-nav {
  padding: 0 16px;
}
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 8px;
}
.nav-link {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.nav-icon i {
  font-size: 16px;
}

.nav-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}
