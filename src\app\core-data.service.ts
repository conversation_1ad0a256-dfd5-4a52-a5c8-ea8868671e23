import { Location } from './Locations/Models/location';
import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Observable } from 'rxjs';
import { DropdownData, FormJson } from './home/<USER>/model';
import { workers } from './Worker/Models/Worker-Model';
@Injectable({
  providedIn: 'root'
})
export class CoreDataService {
  private baseUrl: string = "";
  // Forms API
  private getForm = 'api/Form'
  private getAllForm = 'api/Form'
  private saveForm = 'api/Form';
  private getFormByLoc = 'api/Form/Location';

  // FormList API
  private getFormList = 'api/FormListData'
  private getAllFormList = 'api/FormListData'
  private saveFormList = 'api/FormListData';
  private ListByLocation = 'api/FormListData/location';

  //Form_Data_Submission
  private savingFormData = 'api/FormSubmissionData'
  private getFormDataByID = 'api/FormSubmissionData'
  private getAllFormData = 'api/FormSubmissionData'
  private getFormDataByTemplateID = 'api/FormSubmissionData/getformsData'

  //upload and Get images
  private uploadImage = 'api/image';
  private getPicture = 'api/image';
  private getId = 'api/Asure';
  private saveID = 'api/Asure';

  // Location API
  private location = 'api/Location';
  private LocationforGrid = 'api/Location/LocationData';

  // Users API
  private User = 'api/User';


  constructor(private http: HttpClient) {

      if (window.location.origin.includes("localhost")) {
        // this.baseUrl = "https://safeflow.eudemonic.co.in/api/";
        // this.baseUrl = "https://formflow.eudemonic.co.in/api/";
        this.baseUrl = "https://localhost:44305/";

      }
      else {
        this.baseUrl = window.location.origin + "/api/";
      }

    this.getForm = this.baseUrl + this.getForm;
    this.getAllForm = this.baseUrl + this.getAllForm;
    this.saveForm = this.baseUrl + this.saveForm;
    this.getFormByLoc = this.baseUrl + this.getFormByLoc;

    this.getFormList = this.baseUrl + this.getFormList;
    this.getAllFormList = this.baseUrl + this.getAllFormList;
    this.saveFormList = this.baseUrl + this.saveFormList;
    this.ListByLocation = this.baseUrl + this.ListByLocation;

    this.savingFormData = this.baseUrl + this.savingFormData;
    this.getAllFormData = this.baseUrl + this.getAllFormData;
    this.getFormDataByID = this.baseUrl + this.getFormDataByID;
    this.getFormDataByTemplateID = this.baseUrl + this.getFormDataByTemplateID;

    this.uploadImage = this.baseUrl + this.uploadImage;
    this.getPicture = this.baseUrl + this.getPicture;
    this.getId = this.baseUrl + this.getId;
    this.saveID = this.baseUrl + this.saveID;

    this.location = this.baseUrl + this.location;
    this.LocationforGrid = this.baseUrl + this.LocationforGrid;

    this.User = this.baseUrl + this.User;
  }

  getFormByID(id: string):Observable<any> {
    return this.http.get(this.getForm+`/${id}`);
  }
  getAllforms():Observable<any> {
    return this.http.get(this.getAllForm);
  }
  saveFormTemplate(form: FormJson):Observable<any> {
    return this.http.post(this.saveForm, form);
  }
  GetFORMbyLoc(Loc: string):Observable<any>{
    return this.http.get(this.getFormByLoc+`/${Loc}`);
  }

  getFormListByID(id: string):Observable<any> {
    return this.http.get(this.getFormList+`/${id}`);
  }
  getallFormList():Observable<any> {
    return this.http.get(this.getAllFormList);
  }
  getListofLocation(Loc:string):Observable<any>{
     return this.http.get(this.ListByLocation+`/${Loc}`);
  }

  saveFormlist(form: DropdownData):Observable<any> {
    return this.http.post(this.saveFormList, form,  {
      responseType: 'text' as 'json' });
  }
  saveFormData(form: any):Observable<any> {
    return this.http.post(this.savingFormData, form, {
      responseType: 'text' as 'json' });
  }
  getFormDatabyid(id: string){
    return this.http.get(this.getFormDataByID+`/${id}`);
  }
  getFormDatabyTemplateId(id: string){
    return this.http.get(this.getFormDataByTemplateID+`/${id}`);
  }
  getAllFormsData():Observable<any>{
    return this.http.get(this.getAllFormData);
  }

  saveImage(image: File): Observable<any> {
    const formData = new FormData();
    formData.append("image", image);
    return this.http.post(this.uploadImage, formData);
  }
  getImage(imagePath: string): Observable<Blob> {
    return this.http.get(`${this.getPicture}?filename=${imagePath}`, {
      responseType: 'blob'
    });
  }

   getAddressFromCoords(lat: number, lng: number) {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=AIzaSyCwF16DnyFmK967LUYfDR5gLGbe_v8_zCs`;
    return this.http.get<any>(url);
  }

  GetAsureID():Observable<any>{
    return this.http.get(this.getId);
  }
  saveAsureID(clientid: string , tenantid: string , asureID:string):Observable<any>{
    const payload = {
    id: asureID,
    clientId: clientid,
    tenantId: tenantid,
  };
    return this.http.post(this.saveID ,payload);
  }

  getLocation():Observable<any>{
    return this.http.get(this.location);
  }

  getLocationByID(id: string):Observable<any>{
    return this.http.get(this.location+`/${id}`);
  }

  SaveLocation(Loc: Location){
    return this.http.post(this.location , Loc);
  }
  GetLocationForGrid():Observable<any>{
    return this.http.get(this.LocationforGrid);
  }
  assignUsertoLocation(locationid: string, UserID: string):Observable<any>{
    return this.http.post(this.location + `/location/${locationid}/assign-user/${UserID}`, null, {
      responseType: 'text' as 'json' });
  }
  removeUsertoLocation(locationid: string, UserID: string):Observable<any>{
    return this.http.delete(this.location + `/location/${locationid}/remove-user/${UserID}`, {
      responseType: 'text' as 'json' });
  }

  GetUsers():Observable<any>{
    return this.http.get(this.User);
  }
  GetUserByID(id:string):Observable<any>{
    return this.http.get(this.User+`/${id}`);
  }
  SaveUser(USER: workers){
    return this.http.post(this.User , USER);
  }

  assignLocationtoUser(UserID: string, locationID:string):Observable<any>{
    return this.http.post(this.User+ `/User/${UserID}/assign-Location/${locationID}`, null, {
      responseType: 'text' as 'json' });
  }

  removeLocationtoUser(UserID: string, locationID:string):Observable<any>{
    return this.http.delete(this.User+`/User/${UserID}/remove-Location/${locationID}`, {
      responseType: 'text' as 'json' });
  }

}
