import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './Authentication/Services/auth.guard';

const routes: Routes = [

  { path: '', redirectTo: '/form-view', pathMatch: 'full' },
  // {
  //   path: 'auth',
  //   loadChildren: () => import('./Authentication/auth-routing.module').then(m => m.AuthRoutingModule)

  // },
  {
    path: 'home',
    loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),
    // canActivate: [AuthGuard]
  },
  {
    path: 'form/admin',
    loadChildren: () => import('./form-builder/form-builder.module').then(m => m.FormBuilderModule),
    // canActivate: [AuthGuard]
  },
  {
    path: 'Company/admin',
    loadChildren: () => import('./Company/companys.module').then(m=>m.CompanysModule),
  },
  {
    path: 'Locations',
    loadChildren: () => import('./Locations/location.module').then(m => m.LocationModule),
  },
  {
    path: 'Users',
    loadChildren:() => import('./Worker/workers.module').then(m=>m.WorkersModule),
  }
  // {
  //   path: '**',
  //   redirectTo: '/auth/login',
  //   pathMatch: 'full'
  // }

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
