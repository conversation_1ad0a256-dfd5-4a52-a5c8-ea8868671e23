<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Sign Up</h2>
      <p>Create a new account to get started.</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">{{error}}</div>

    <form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
      <div class="form-row">
        <div class="form-group col-md-6">
          <label for="firstName">First Name</label>
          <input 
            type="text" 
            formControlName="firstName" 
            class="form-control" 
            [ngClass]="{ 'is-invalid': submitted && f['firstName'].errors }" 
            placeholder="First name"
          />
          <div *ngIf="submitted && f['firstName'].errors" class="invalid-feedback">
            <div *ngIf="f['firstName'].errors['required']">First name is required</div>
          </div>
        </div>

        <div class="form-group col-md-6">
          <label for="lastName">Last Name</label>
          <input 
            type="text" 
            formControlName="lastName" 
            class="form-control" 
            [ngClass]="{ 'is-invalid': submitted && f['lastName'].errors }" 
            placeholder="Last name"
          />
          <div *ngIf="submitted && f['lastName'].errors" class="invalid-feedback">
            <div *ngIf="f['lastName'].errors['required']">Last name is required</div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input 
          type="email" 
          formControlName="email" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" 
          placeholder="Enter your email"
        />
        <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
          <div *ngIf="f['email'].errors['required']">Email is required</div>
          <div *ngIf="f['email'].errors['email']">Please enter a valid email address</div>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input 
          type="password" 
          formControlName="password" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" 
          placeholder="Enter your password"
        />
        <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
          <div *ngIf="f['password'].errors['required']">Password is required</div>
          <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input 
          type="password" 
          formControlName="confirmPassword" 
          class="form-control" 
          [ngClass]="{ 'is-invalid': submitted && f['confirmPassword'].errors }" 
          placeholder="Confirm your password"
        />
        <div *ngIf="submitted && f['confirmPassword'].errors" class="invalid-feedback">
          <div *ngIf="f['confirmPassword'].errors['required']">Confirm Password is required</div>
          <div *ngIf="f['confirmPassword'].errors['passwordMismatch']">Passwords must match</div>
        </div>
      </div>

      <div class="form-group">
        <button [disabled]="loading" class="btn btn-primary btn-block">
          <span *ngIf="loading" class="spinner-border spinner-border-sm mr-1"></span>
          Sign Up
        </button>
      </div>

      <div class="auth-links">
        <a routerLink="/auth/login">Already have an account? Login</a>
      </div>
    </form>
  </div>
</div>
