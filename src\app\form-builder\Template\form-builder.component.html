<div class="form-builder-container">

  <!-- Sidebar -->
  <div class="sidebar">
    <!-- Forms List View -->
    <div class="sidebar-header">
      <h3>Form Builder</h3>
      <button class="add-form-btn" (click)="startNewForm()">
        <i class="fas fa-plus"></i>
      </button>
    </div>
    <div class="sidebar-nav">
      <ul class="nav-list">
        <li *ngFor="let form of this.formsListforBuilder" class="nav-item">
          <div class="nav-link"
               [class.active]="selectedFormId === form.id"
               (click)="selectForm(form)">
            <span class="nav-text">{{ form.auditHistory.formName || 'Unnamed Form' }}</span>
          </div>
        </li>
      </ul>
      <div *ngIf="this.formsListforBuilder.length === 0" class="no-forms">
        No forms available. Click + to create one.
      </div>
    </div>
  </div>
  <!-- Main Content -->
  <div class="main-content">

    <!-- Form Builder Content -->
    <ng-container>
      <!-- Initial State - Only New Form Button -->
      <div class="initial-state" *ngIf="!selectedFormId && (!formData || formData.component.length === 0)">
        <div class="no-selection">
          <p>Select a form from the sidebar to edit or create a new one</p>
          <button class="new-form-btn" (click)="startNewForm()">
            <i class="fas fa-plus-circle"></i> New Form
          </button>
        </div>
      </div>


      <div class="box-content">

      <!-- Main Form Building Area - Only shown when form is selected -->
      <div class="form-building-area" *ngIf="selectedFormId && formData && formData.component.length > 0">
        <!-- Form Header with Form Name -->
        <div class="form-header">
          <h2 *ngIf="formData.auditHistory.formName">{{ formData.auditHistory.formName || 'Untitled Form' }}</h2>
          <div class="form-metadata">
            <div class="required-fields-note">* Indicates Required Fields</div>
          </div>
        </div>

        <!-- Form Sections -->
        <div class="form-preview">
          <div *ngFor="let section of formData.component; let sectionIndex = index" class="form-section">
            <!-- Section Header -->
            <div class="section-header">
              <div class="section-title">
                <i class="fas fa-bars drag-handle"></i>
                <input type="text" [(ngModel)]="section.title" (ngModelChange)="updateSectionTitle(section, $event)" placeholder="New Section">
              </div>
              <div class="section-actions">
                <div class="section-toggles">
                  <div class="toggle-option">
                    <input type="checkbox" id="collapsible-{{sectionIndex}}"
                          [(ngModel)]="section.canCollapsed"
                          (change)="section.isCollapsed = section.canCollapsed ? section.isCollapsed : false">
                    <label for="collapsible-{{sectionIndex}}">Collapsible</label>
                  </div>

                  <div class="toggle-option">
                    <input type="checkbox" id="repeatable-{{sectionIndex}}"
                    [(ngModel)]="section.repeatable"
                    (change)="section.repeatable = section.repeatable ? section.repeatable : false">
                    <label for="repeatable-{{sectionIndex}}">Repeatable</label>
                  </div>
                  <div class="toggle-option">
                    <input type="checkbox" id="conditional-{{sectionIndex}}"
                          [(ngModel)]="section._isConditional"
                          (change)="handleConditionToggle(section)">
                    <label for="conditional-{{sectionIndex}}">Conditional Section</label>
                  </div>
                </div>
                <button class="btn-icon collapse-btn" *ngIf="section.canCollapsed" (click)="toggleSectionCollapse(section)">
                  <i class="fas" [ngClass]="{'fa-chevron-down': !section.isCollapsed, 'fa-chevron-up': section.isCollapsed}"></i>
                </button>
                <button class="btn-icon delete-section" (click)="formData.component.splice(sectionIndex, 1)">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>

<!-- Condition Popup -->
<div *ngIf="section === activeConditionSection" class="condition-popup-backdrop">
  <div class="condition-popup">
    <div class="popup-header">
      <h3><i class="fas fa-code-branch"></i> Set Section Condition</h3>
      <button class="close-btn" (click)="cancelCondition()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="popup-content">
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-tag"></i> Field Name
        </label>
        <select [(ngModel)]="tempCondition.field" class="form-select" placeholder="Select field">
          <option value="" disabled>Select a field</option>
          <option *ngFor="let field of availableFields" [value]="field.fieldName">
            {{ field.label }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-equals"></i> Operator
        </label>
        <select [(ngModel)]="tempCondition.operator" class="form-select">
          <option value="==">Equals to</option>
          <option value="!=">Not Equals </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-keyboard"></i> Value
        </label>
        <input type="text"
               [(ngModel)]="tempCondition.value"
               class="form-input"
               placeholder="Enter comparison value" />
      </div>

      <div class="condition-preview" *ngIf="tempCondition.field && tempCondition.value">
        <div class="preview-header">
          <i class="fas fa-eye"></i> Preview
        </div>
        <div class="preview-text">
          Show this section when <strong>{{ tempCondition.field }}</strong>
          <span class="operator">{{ tempCondition.operator === '==' ? 'equals' : 'does not equal' }}</span>
          <strong>"{{ tempCondition.value }}"</strong>
        </div>
      </div>
    </div>

    <div class="popup-actions">
      <button class="btn-apply" (click)="applyCondition()" [disabled]="!tempCondition.field || !tempCondition.value">
        <i class="fas fa-check"></i> Apply Condition
      </button>
      <button class="btn-cancel" (click)="cancelCondition()">
        <i class="fas fa-times"></i> Cancel
      </button>
    </div>
  </div>
</div>


            <!-- Section Content -->
            <div class="section-content" *ngIf="!section.isCollapsed">
              <div class="empty-section-message" *ngIf="section.elements.length === 0">
                <p>This section is empty. Add form elements.</p>
              </div>

              <!-- Form Elements would go here -->
              <div class="form-elements-list">
                <div *ngFor="let element of section.elements; let elementIndex = index"
                     class="form-element-row"
                     [class.selected-element]="selectedElement === element"
                     (click)="selectElementForValidation(element, section)">
                  <!-- Required Star Icon -->
                  <div class="element-required" (click)="toggleRequired(element); $event.stopPropagation()">
                    <i class="fas fa-star" [ngClass]="{'required': element.attributes.is_required, 'not-required': !element.attributes.is_required}"></i>
                  </div>

                  <!-- Element Type Icon -->
                  <div class="element-type-icon">
                    <i *ngIf="element.type === 'text'" class="fas fa-font"></i>
                    <!-- <i *ngIf="element.type === 'email'" class="fas fa-envelope"></i>
                    <i *ngIf="element.type === 'number'" class="fas fa-hashtag"></i> -->
                    <i *ngIf="element.type === 'link'" class="fas fa-link"></i>
                    <i *ngIf="element.type === 'Select'" class="fas fa-check-square"></i>
                  </div>

                  <!-- Element Label and Placeholder/Link Text -->
                  <div class="element-fields" (click)="$event.stopPropagation()">
                    <div class="element-label">
                      <input type="text" [(ngModel)]="element.attributes.label"
                            (ngModelChange)="updateFieldName(element, $event, section.title)"
                            placeholder="Label">
                    </div>
                    <!-- For regular elements with placeholder -->
                    <div class="element-placeholder" *ngIf="element.type !== 'map' && element.type !== 'date' && element.type !== 'link' && element.type !== 'signature'  ">
                      <input type="text" [(ngModel)]="element.attributes.placeholder_text" placeholder="Placeholder">
                    </div>
                    <!-- For link elements -->
                    <div class="element-link-text" *ngIf="element.type === 'link'">
                      <input type="text" [(ngModel)]="element.attributes.link_text">
                    </div>
                    <!-- For dropdown elements -->
                    <!-- <div class="element-dropdown-info" *ngIf="element.type === 'select'">
                      <span class="dropdown-source">List ID: {{ element.attributes.dataListId }}</span>
                    </div> -->
                  </div>

                  <!-- Show Label Checkbox -->
                  <div class="element-show-label" (click)="$event.stopPropagation()">
                    <div class="checkbox-wrapper">
                      <input type="checkbox" [(ngModel)]="element.attributes.show_label"
                            id="show-label-{{sectionIndex}}-{{elementIndex}}">
                      <label for="show-label-{{sectionIndex}}-{{elementIndex}}">Show Label</label>
                    </div>

                    <!-- Multiselect Checkbox for Dropdown -->
                    <div class="checkbox-wrapper" *ngIf="element.type === 'Select'">
                      <input type="checkbox" [(ngModel)]="element.multiselect"

                            id="multiselect-{{sectionIndex}}-{{elementIndex}}">
                      <label for="multiselect-{{sectionIndex}}-{{elementIndex}}">Multiselect</label>
                    </div>
                  </div>

                  <!-- Element Actions -->
                  <div class="element-actions">
                    <button class="btn-icon delete-btn" (click)="deleteElement(section, elementIndex); $event.stopPropagation()">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                  <div class="options-wrapper" (click)="$event.stopPropagation()">
                  <div class="options" >
                    <i class="fas fa-ellipsis-v"></i>
                    <div class="options-menu">
                      <div class="menu-item" (click)="handleComment('comment', element)" title="Add Comment">
                        <i class="fas fa-comment"  [ngClass]="{'required': element.attributes.actions?.comment, 'not-required': !element.attributes.actions?.comment}"></i>
                      </div>
                      <div class="menu-item" (click)="handleImages('image', element)" title="Add Image">
                        <i class="fas fa-image"  [ngClass]="{'required': element.attributes.actions?.camera, 'not-required': !element.attributes.actions?.camera}"></i>
                      </div>
                      <div class="menu-item" (click)="handleFlag('flag', element)" title="Add flag">
                      <i class="fas fa-flag"  [ngClass]="{'required': element.attributes.actions?.flag, 'not-required': !element.attributes.actions?.flag}"></i>
                    </div>
                    <div class="menu-item" (click)="configureMiniForm('followupForm', element)" title="Add Follow-Up">
                      <i class="fas fa-file-signature"  [ngClass]="{'required': element.attributes.actions?.followupForm}"></i>
                      <!-- , 'not-required': !element.attributes.actions?.followupForm -->
                    </div>
                    </div>
                  </div>
                  </div>
                </div>
              </div>

              <!-- Add Item Button -->
              <div class="add-item-container">
                <button class="btn-add-small" (click)="showElementSelectionSidebar(section)">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
            </div>
          </div>

          <!-- Add Section Button -->
          <div class="add-section-container">
            <button class="btn-add-small" (click)="addSection()">
              <i class="fas fa-plus"></i> Add Section
            </button>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button class="btn-save" (click)="saveForm()">
            <i class="fas fa-save"></i> Save Form
          </button>
        </div>
      </div>

      <!-- Validation Panel -->
      <div class="validation-box" *ngIf="selectedElement">
        <div class="validation-header">
          <h4>Validation Settings</h4>
          <div class="selected-element-info">
            <span class="element-type-badge">{{ selectedElement.type }}</span>
            <span class="element-label">{{ selectedElement.attributes.label || 'Unnamed Element' }}</span>
          </div>
          <button class="close-validation-btn" (click)="clearSelectedElement()">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="validation-content">
          <!-- Required Field Toggle -->
          <div class="validation-group">
            <label class="validation-label">
              <input type="checkbox"
                     [checked]="selectedElement.attributes.is_required"
                     (change)="selectedElement.attributes.is_required = !selectedElement.attributes.is_required">
              Required Field
            </label>
          </div>

          <!-- Email Field Info -->
          <!-- <div *ngIf="selectedElement.type === 'email' || (selectedElement.attributes.label && selectedElement.attributes.label.toLowerCase().includes('email'))" class="validation-group">
            <div class="email-validation-info">
              <i class="fas fa-info-circle"></i>
              <span>Email fields automatically validate email format. Only required validation is available.</span>
            </div>
          </div> -->

          <!-- Text/Textarea Validations -->
          <div *ngIf="isValidationApplicable('minlength')" class="validation-group">
            <label class="validation-label">Minimum Length:</label>
            <input type="number"
                   class="validation-input"
                   [value]="getValidationValue('minlength')"
                   (input)="onValidationInputChange($event, 'minlength')"
                   placeholder="e.g., 3">
          </div>

          <div *ngIf="isValidationApplicable('maxlength')" class="validation-group">
            <label class="validation-label">Maximum Length:</label>
            <input type="number"
                   class="validation-input"
                   [value]="getValidationValue('maxlength')"
                   (input)="onValidationInputChange($event, 'maxlength')"
                   placeholder="e.g., 50">
          </div>



          <!-- Number Validations -->
          <div *ngIf="isValidationApplicable('min')" class="validation-group">
            <label class="validation-label">Minimum Value:</label>
            <input type="number"
                   class="validation-input"
                   [value]="getValidationValue('min')"
                   (input)="onValidationInputChange($event, 'min')"
                   placeholder="e.g., 0">
          </div>

          <div *ngIf="isValidationApplicable('max')" class="validation-group">
            <label class="validation-label">Maximum Value:</label>
            <input type="number"
                   class="validation-input"
                   [value]="getValidationValue('max')"
                   (input)="onValidationInputChange($event, 'max')"
                   placeholder="e.g., 100">
          </div>

          <!-- Date Validations -->
          <div *ngIf="isValidationApplicable('minDate')" class="validation-group">
            <label class="validation-label">Minimum Date:</label>
            <input type="date"
                   class="validation-input"
                   [value]="getValidationValue('minDate')"
                   (input)="onValidationInputChange($event, 'minDate')">
          </div>

          <div *ngIf="isValidationApplicable('maxDate')" class="validation-group">
            <label class="validation-label">Maximum Date:</label>
            <input type="date"
                   class="validation-input"
                   [value]="getValidationValue('maxDate')"
                   (input)="onValidationInputChange($event, 'maxDate')">
          </div>

          <div *ngIf="isValidationApplicable('noFuture')" class="validation-group">
            <label class="validation-label">
              <input type="checkbox"
                     [checked]="getValidationValue('noFuture') === 'true' || getValidationValue('noFuture') === true"
                     (change)="onValidationCheckboxChange($event, 'noFuture')">
              No Future Dates
            </label>
          </div>

          <div *ngIf="isValidationApplicable('noPast')" class="validation-group">
            <label class="validation-label">
              <input type="checkbox"
                     [checked]="getValidationValue('noPast') === 'true' || getValidationValue('noPast') === true"
                     (change)="onValidationCheckboxChange($event, 'noPast')">
              No Past Dates
            </label>
          </div>

          <!-- File Validations -->
          <div *ngIf="isValidationApplicable('maxSize')" class="validation-group">
            <label class="validation-label">Maximum File Size (MB):</label>
            <input type="number"
                   class="validation-input"
                   [value]="getValidationValue('maxSize')"
                   (input)="onValidationInputChange($event, 'maxSize')"
                   placeholder="e.g., 5">
          </div>
        </div>
      </div>

      <!-- No Element Selected Message -->
      <!-- <div class="validation-box no-selection" *ngIf="!selectedElement && selectedFormId">
        <div class="no-selection-content">
          <i class="fas fa-mouse-pointer"></i>
          <h4>Select an Element</h4>
          <p>Click on any form element to configure its validation settings</p>
        </div>
      </div> -->
</div>
  <!-- Create Form Modal -->
  <div class="modal" *ngIf="showCreateFormModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Create New Form</h3>
        <button class="close-btn" (click)="showCreateFormModal = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="createFormForm" (ngSubmit)="createNewForm()">
          <div class="form-group">
            <label for="newFormName">Form Name</label>
            <input type="text" id="newFormName" formControlName="formName" placeholder="Enter form name">
          </div>
          <div class="form-group">
            <label for="newUserName">User Name</label>
            <input type="text" id="newUserName" formControlName="userName" placeholder="Enter your username">
          </div>

          <div class="modal-actions">
            <button type="button" class="btn-secondary" (click)="showCreateFormModal = false">Cancel</button>
            <button type="submit" class="btn-primary">Create Form</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add Element Type Modal -->
  <div class="modal" *ngIf="showElementSelection">
    <div class="modal-content element-type-modal">
      <div class="modal-header">
        <h3>Create New Item Type</h3>
        <button class="close-btn" (click)="hideElementSelectionSidebar()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body element-type-list" style="padding: 0;">
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTextFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-font"></i>
            </div>
            <div class="element-type-name">Text</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addEmailFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="element-type-name">Email</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addPhoneNumberFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-hashtag"></i>
            </div>
            <div class="element-type-name">Phone Number</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addNumberFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-hashtag"></i>
            </div>
            <div class="element-type-name">Number</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="toggleDropdownLists()">
            <div class="element-type-icon">
              <i class="fas fa-check-square"></i>
            </div>
            <div class="element-type-name">Drop Down</div>
          </div>
          <div class="dropdown-lists" *ngIf="showDropdownLists">
            <div class="dropdown-list-header">Select a list:</div>
            <div class="dropdown-search">
              <input type="text"
                     [(ngModel)]="listSearchTerm"
                     (input)="filterLists()"
                     placeholder="Search lists..."
                     class="dropdown-search-input">
            </div>
            <div class="dropdown-list-items">
              <div *ngIf="this.formLists.length === 0" class="no-lists-message">
                No lists available. Please create lists first.
              </div>
              <div *ngIf="this.formLists.length > 0 && filteredFormLists.length === 0" class="no-lists-message">
                No matching lists found.
              </div>
              <div *ngFor="let list of filteredFormLists" class="dropdown-list-item" (click)="selectFormList(list)">
                {{ list.auditHistory.listName }}
              </div>
            </div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addUserList()">
            <div class="element-type-icon">
              <i class="fas fa-align-left"></i>
            </div>
            <div class="element-type-name">Select user</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addLocationElement()">
            <div class="element-type-icon">
              <i class="fas fa-align-left"></i>
            </div>
            <div class="element-type-name">Location</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTextAreaFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-align-justify"></i>
            </div>
            <div class="element-type-name">Text Area</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addImageElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Image</div>
          </div>
        </div>
        <!-- ********************** -->
         <!-- Guide Image Element -->
         <div class="element-type-item">
          <div class="element-type-header" (click)="toggleGuideImageUpload($event)" >

            <div class="element-type-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="element-type-name">Image for Guide</div>
          </div>
          <div class="element-type-url-input" *ngIf="showGuideImageUpload">
            <input type="file" accept=".jpg, .jpeg, .png" (change)="onGuideImageSelect($event)">
            <div *ngIf="guideImageUrl" class="guide-image-preview">
              <img [src]="guideImageUrl" alt="Guide Image Preview" style="max-width: 100%; max-height: 150px;">
            </div>
            <button class="btn-add-url" (click)="addGuideImageElement()">Done</button>
          </div>
        </div>
        <!--*********************  -->
        <div class="element-type-item">
          <div class="element-type-header" (click)="addDateFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="element-type-name">Select Date</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTimeFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="element-type-name">Select Time</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addSignatureFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Signature</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addPDFFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Insert PDF</div>
          </div>
        </div>
         <div class="element-type-item">
          <div class="element-type-header" (click)="togglePdfUpload($event)" >

            <div class="element-type-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="element-type-name">View PDF</div>
          </div>
          <div class="element-type-url-input" *ngIf="showPdfUpload">
            <input type="file" accept="application/pdf" (change)="onPdfSelect($event)">
            <div *ngIf="guideImageUrl" class="guide-image-preview">
              <img [src]="guideImageUrl" alt="Guide Image Preview" style="max-width: 100%; max-height: 150px;">
            </div>
            <button class="btn-add-url" (click)="AddviewPDFFieldElement()">Done</button>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addQRScannerElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">QR Scanner</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="toggleLinkUrlInput($event)">
            <div class="element-type-icon">
              <i class="fas fa-link"></i>
            </div>
            <div class="element-type-name">Add Link</div>
          </div>
          <div class="element-type-url-input" *ngIf="showLinkUrlInput">
            <input type="text" [(ngModel)]="linkUrl" placeholder="Enter URL (e.g., https://example.com)">
            <button class="btn-add-url" (click)="addLinkElement()">Done</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mini Form Selector Modal -->
  <ng-template [ngIf]="showMiniFormSelectorModal">
  <div class="modal-overlay">
    <div class="modal">
      <h3>Select Follow-up Form</h3>

      <select [(ngModel)]="selectedFollowUpFormId">
        <option *ngFor="let form of this.formsListforBuilder" [value]="form.id">
          {{ form.auditHistory.formName }}
        </option>
      </select>

      <div class="modal-actions">
        <button (click)="applyFollowUpForm()">Save</button>
        <button (click)="closeFollowUpFormSelector()">Cancel</button>
      </div>
    </div>
  </div>
</ng-template>

    </ng-container>
  </div>
</div>
