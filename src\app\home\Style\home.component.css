/* Base styles */
.form-container {
  display: flex;
  width: 100%;
  max-width: 600px;
  margin: 0px 6vw;
  padding: 20px;
  box-sizing: border-box;
  gap: 2vw;
  flex-direction: column;
}

/* Centered form for shared links */
.form-container.centered-form {
  margin: 0px 20vw;
  max-width: none;
  width: auto;
}

/* Responsive styles for centered form */
@media (max-width: 1200px) {
  .form-container.centered-form {
    margin: 0px 15vw;
  }
}

@media (max-width: 768px) {
  .form-container.centered-form {
    margin: 0px 5vw;
  }
}

@media (max-width: 576px) {
  .form-container.centered-form {
    margin: 0px 2vw;
  }
}

/* Form Name Bar Styles */
.form-name-bar {
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 10px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0px;
  z-index: 10;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-name-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.form-name-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.form-actions  {
  background-color: transparent;
  border: none;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  color: #555;
  transition: all 0.2s ease;
}

.form-actions  {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.form-actions  {
  font-size: 18px;
}

/* Mobile styles for form name bar */
@media (max-width: 767px) {
  .form-name-bar {
    padding: 8px 10px;
    top: 0px;
  }

  .form-name-title {
    font-size: 13px;
  }
}


/* Mobile-first approach */
@media (max-width: 767px) {
  .form-container {
    display: flex;
    padding: 15px 10px;
    max-width: 100%;
    margin: 0;
    flex-direction: column;
  }
}

.eye {
  border: none;
  border-radius: 8px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.eye:hover {
  background: black;
  color: white;
}

.eye i {
  font-size: 16px;
}

/* Share Button Styles */
.share-btn {
  border: none;
  border-radius: 8px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.share-btn:hover {

  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.share-btn i {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.share-btn:hover i {
  transform: rotate(15deg);
}

.close-full-window {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: whitesmoke;
}

.close-full-window:hover {
  color: white;
}
.form-section {
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 60vw;
}

.form-group {
  margin-bottom: 15px;
  position: relative;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.form-group:hover {
  background-color: rgba(33, 150, 243, 0.03);
}

@media (max-width: 767px) {
  .form-section {
    padding: 12px 10px;
    border-radius: 8px;
    margin-bottom: 15px;
    width: 100%; /* Full width on mobile */
  }

  .form-group {
    margin-bottom: 12px;
    padding: 6px;
  }
}
.form-group label {
  background-color: white;
  display: block;
  font-weight: bold;
  color: silver;
  margin-bottom: 5px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-group input,
.form-group select,
.form-group textarea,
.form-group app-custom-dropdown,
.form-group app-grouped-dropdown {
  width: 100%;
  height: 7vh;
  padding: 3px;
  border-radius: 4px;
  border: none;
  border-bottom: 3px solid #cccc;
  box-sizing: border-box;
  font-size: 16px;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease;
}

/* Hover effect - blue bottom border */
.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover,
.form-group app-custom-dropdown:hover,
.form-group app-grouped-dropdown:hover {
  border-bottom-color: #2196F3;
}

/* Focus effect - darker blue bottom border */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus,
.form-group app-custom-dropdown:focus,
.form-group app-grouped-dropdown:focus {
  border-bottom-color: #0b7dda;
  outline: none;
}

.form-group input[type="file"] {
  padding: 6px;
  border: 1px dashed #ccc;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.form-group input[type="file"]:hover {
  border-color: #2196F3;
  background-color: #f0f8ff;
}

.form-group input[type="file"]:focus {
  border-color: #0b7dda;
  outline: none;
}

@media (max-width: 767px) {
  .form-group label {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea,
  .form-group app-custom-dropdown,
  .form-group app-grouped-dropdown {
    padding: 6px;
    font-size: 14px;
  }

  .form-group input[type="file"] {
    padding: 4px;
    font-size: 13px;
  }
}

.form-button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #2196F3;
  color: white;
  font-weight: bold;
  transition: background-color 0.3s ease;
  min-width: 120px;
}

.form-button:hover {
  background-color: #0b7dda;
}

.form-button:active {
  background-color: #0a6fc7;
}

@media (max-width: 767px) {
  .form-button {
    padding: 8px 12px;
    font-size: 14px;
    min-width: 100px;
    width: 100%;
    margin-top: 10px;
  }
}

.error-message {
  color: #e53935;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}

@media (max-width: 767px) {
  .error-message {
    font-size: 11px;
    margin-top: 3px;
  }
}
.signature-pad {
  border: 1px solid #ccc;
  width: 100%;
  height: 150px;
  cursor: crosshair;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  touch-action: none; /* Prevents scrolling while signing on touch devices */
  display: block; /* Ensures the canvas is displayed as a block element */
}

@media (max-width: 767px) {
  .signature-pad {
    height: 120px;
  }
}
.switch {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:checked + .slider:before {
  transform: translateX(14px);
}
.date-input {
  font-size: 14px;
}
.form-guide-image {
  max-width: 100%;
  height: auto;
  max-height: 500px;
  margin: 10px 0;
  border-radius: 8px;
  border: 1px solid #ddd;
  object-fit: contain;
}

/* Section header styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* Section heading styles */
.form-section h3 {
  margin-top: 0;
  margin-bottom: 0;
  color: #333;
  font-size: 18px;
  padding-bottom: 8px;
  flex: 1;
}

/* Collapsible section styles */
.form-section h3.collapsible {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
  padding: 8px;
  border-radius: 4px;
}

/* Repeat section button */
.repeat-section-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.3s;
}

.repeat-section-btn:hover {
  background-color: #45a049;
}

.repeat-section-btn i {
  font-size: 12px;
}

.form-section h3.collapsible:hover {
  background-color: #f5f5f5;
}

.collapse-icon {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
  transition: transform 0.3s ease;
}

@media (max-width: 767px) {
  .form-guide-image {
    max-height: 300px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .form-section h3 {
    font-size: 16px;
    padding-bottom: 6px;
    width: 100%;
  }

  .form-section h3.collapsible {
    padding: 6px;
  }

  .collapse-icon {
    font-size: 12px;
  }

  .repeat-section-btn {
    font-size: 12px;
    padding: 5px 10px;
    width: 100%;
    justify-content: center;
  }
}
.action-icons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0px;
  right: 16px;
  padding: 0px;
  margin-top: 0px;
}

.action-icons a {
  color: #555;
  font-size: 16px;
  text-decoration: none;
  transition: color 0.2s;
  padding: 5px;
  border-radius: 4px;
}

.action-icons a:hover {
  color: #2196F3;
  background-color: rgba(33, 150, 243, 0.1);
}

.action-icons a:active {
  color: #0b7dda;
}

.uploaded-file-name {
  font-size: 13px;
  color: #989898;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.uploaded-file-name i {
  color: #2196F3;
}

/* Comment box styling */
.comment-box, .form-textarea {
  margin-top: 10px;
  height: 7vh;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  resize: vertical; /* Only allow vertical resizing */
  overflow-x: hidden; /* Hide horizontal overflow */
  min-height: 60px;
  padding: 8px;
  font-family: inherit;
  font-size: 14px;
}

@media (max-width: 767px) {
  .action-icons {
    gap: 5px;
    padding: 3px;
  }

  .action-icons a {
    font-size: 14px;
    padding: 4px;
  }

  .uploaded-file-name {
    font-size: 12px;
    margin-top: 3px;
  }

  .comment-box, .form-textarea {
    font-size: 16px; /* Larger font for better mobile readability */
    padding: 10px;
    margin-top: 8px;
    min-height: 50px;
    height: auto;
    border-radius: 6px;
  }
}


/* QR Scanner Styles */
.scanner-container {
  margin-top: 10px;
  margin-bottom: 15px;
}

.scanner-controls {
  margin-bottom: 10px;
}

.scan-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.scan-button:hover {
  background-color: #45a049;
}

.scanner-wrapper {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.close-scanner-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  z-index: 10;
}

.scan-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
}

.result-label {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.result-value {
  word-break: break-all;
  font-family: monospace;
  background-color: #fff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

@media (max-width: 767px) {
  .scanner-wrapper {
    max-width: 100%;
  }

  .scan-button {
    width: 100%;
    padding: 10px;
  }
}

/* demo */
.form-details-content {
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1px;
  padding: 0px;
}

.metadata-row {
  display: flex;
  margin-bottom: 2px;
}

.metadata-label {
  font-size: 0.9rem;
  font-weight: bold;
  min-width: 120px;
  color: #555;
}

.metadata-value {
  color: #333;
  font-size: 0.9rem;
}

.scanner-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.scanner-input {
  width: 100%;
  padding-right: 40px; /* Space for QR icon */
  padding: 8px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.qr-icon-button {
  position: absolute;
  right: 5px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 20px;
  color: #007bff;
}

.qr-icon-button:hover {
  color: #0056b3;
}

.form-actions {
display: flex;
justify-content: space-between;
align-items: center;
}
@media (max-width: 767px) {
  .form-actions {
    top: 5px;
    right: 5px;
  }

}
.selected {
  font-weight: bold;
  background-color: #d0f0ff;
}
.flag-item{
    font-weight: bold;
    color: #878787;
    cursor: pointer;
    font-size: 0.85rem;
}
.selectedFollowUp{
  background-color: #fff2d3;
  border: 1px solid #ffd900;
  padding: 4px 10px;
  border-radius: 14px;
  font-size: 13px;
  color: #d67530;
}

.btn-submission{
  display: flex;
  justify-content: space-between;
}
/* Manager Review & Signature Section Styles */
/* .manager-review-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  margin-top: 30px;
  padding: 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
} */


.manager-signature-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 0 24px 24px 24px;
}

.signature-field-group {
  display: flex;
  flex-direction: column;
}

.signature-label {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  /* display: flex; */
  align-items: center;
  gap: 5px;
}

.signature-label::after {
  content: "*";
  color: #ef4444;
  font-weight: bold;
}

.signature-pad-container {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manager-signature-pad {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: crosshair;
  display: block;
  margin-bottom: 16px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.signature-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.pen-color-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pen-color-control label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.pen-color-control input[type="color"] {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
}

.signature-buttons {
  display: flex;
  gap: 8px;
}

.clear-signature-btn,
.save-signature-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.clear-signature-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.clear-signature-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.save-signature-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
}

.save-signature-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.signature-status {
  margin-top: 12px;
  padding: 12px;
  background: #d1fae5;
  border: 1px solid #a7f3d0;
  border-radius: 6px;
  color: #065f46;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.signature-status i {
  color: #10b981;
  font-size: 1rem;
}


/* Mobile responsive styles for manager signature */
@media (max-width: 768px) {
  .manager-signature-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 16px 20px 16px;
  }

  .manager-signature-pad {
    width: 100%;
    height: 120px;
  }

  .signature-controls {
    flex-direction: column;
    gap: 12px;
  }

  .signature-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .clear-signature-btn,
  .save-signature-btn {
    flex: 1;
    justify-content: center;
  }
}

/* Manager Signature Modal Styles */
.manager-signature-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.manager-signature-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideInModal 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-body {
  padding: 15px;
}

.manager-name-input {
  margin-bottom:10px;
}

.manager-name-input label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.manager-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.manager-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.debug-info {
  display: block;
  margin-top: 4px;
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
}

.signature-section {
  margin-bottom: 0px;
}

.signature-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 10px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 5px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-btn {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.save-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
}

.save-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* Multiple Signatures Display Styles */
.saved-signatures-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 12px;
  margin-top: 20px;
  padding: 0;
  box-shadow: 0 4px 6px rgba(14, 165, 233, 0.1);
}

.saved-signatures-header {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  padding: 16px 20px;
  border-radius: 10px 10px 0 0;
  margin-bottom: 20px;
}

.saved-signatures-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.saved-signatures-header h3 i {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
}

.saved-signatures-content {
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.signature-item {
  background: white;
  border: 1px solid #e0f2fe;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.signature-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.signature-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #0284c7;
  display: flex;
  align-items: center;
  gap: 8px;
}

.signature-info h4 i {
  font-size: 14px;
  color: #0ea5e9;
}

.signature-timestamp {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
}

.signature-timestamp i {
  font-size: 11px;
}

.remove-signature-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.remove-signature-btn:hover {
  background: #fecaca;
  color: #b91c1c;
  transform: scale(1.1);
}

.signature-display {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.signature-image {
  max-width: 100%;
  height: auto;
  max-height: 120px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Mobile responsive styles for multiple signatures */
@media (max-width: 768px) {
  .saved-signatures-content {
    padding: 0 16px 16px 16px;
  }

  .saved-signatures-header {
    padding: 12px 16px;
  }

  .saved-signatures-header h3 {
    font-size: 1rem;
  }

  .signature-item {
    padding: 12px;
  }

  .signature-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .remove-signature-btn {
    align-self: flex-end;
  }

  .signature-image {
    max-height: 100px;
  }
}

/* PDF Upload Component Styles */
.pdf-upload-container {
  margin-top: 8px;
}

.pdf-file-input {
  width: 100%;
  padding: 8px 12px;
  border: 2px dashed #ccc;
  border-radius: 6px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pdf-file-input:hover {
  border-color: #007bff;
  background-color: #f0f7ff;
}

.pdf-file-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.pdf-file-info {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.pdf-file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pdf-icon {
  color: #dc3545;
  font-size: 20px;
  flex-shrink: 0;
}

.pdf-file-name {
  flex: 1;
  font-weight: 500;
  color: #333;
  word-break: break-word;
}

.pdf-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.btn-view-pdf {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-view-pdf {
  background-color: #007bff;
  color: white;
}

.btn-view-pdf:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.btn-remove-pdf {
  background-color: #dc3545;
  color: white;
}

.btn-remove-pdf:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.pdf-legacy-info {
  margin-top: 12px;
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.pdf-legacy-info .btn-view-pdf {
  background-color: #ffc107;
  color: #212529;
}

.pdf-legacy-info .btn-view-pdf:hover {
  background-color: #e0a800;
}

/* PDF Upload Responsive Design */
@media (max-width: 768px) {
  .pdf-file-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .pdf-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .btn-view-pdf,
  .btn-remove-pdf {
    flex: 1;
    justify-content: center;
  }
  .header-actions{

  }
}

.modal-mini-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mini {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-height: 90vh;
  overflow-y: auto;
}
